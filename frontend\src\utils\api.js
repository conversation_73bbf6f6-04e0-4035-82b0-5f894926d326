import axios from 'axios'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useAuthStore } from '@/stores/auth'
import router from '@/router'

// 创建axios实例
const api = axios.create({
  baseURL: '/api/v1',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    const authStore = useAuthStore()

    // 添加认证token
    if (authStore.token) {
      config.headers.Authorization = `Bearer ${authStore.token}`
    }

    // 添加请求ID用于追踪
    config.headers['X-Request-ID'] = generateRequestId()

    return config
  },
  (error) => {
    console.error('请求拦截器错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    const { data } = response

    // 统一处理响应格式
    if (data.success === false) {
      // 业务错误
      ElMessage.error(data.message || '操作失败')
      return Promise.reject(new Error(data.message || '操作失败'))
    }

    return data
  },
  async (error) => {
    const { response } = error
    const authStore = useAuthStore()

    if (response) {
      const { status, data } = response

      switch (status) {
        case 401:
          // 未授权，清除token并跳转到登录页
          ElMessage.error('登录已过期，请重新登录')
          authStore.logout()
          router.push('/login')
          break

        case 403:
          ElMessage.error('没有权限访问该资源')
          break

        case 404:
          ElMessage.error('请求的资源不存在')
          break

        case 422:
          // 表单验证错误
          if (data.detail && Array.isArray(data.detail)) {
            const errors = data.detail.map(item => item.msg).join(', ')
            ElMessage.error(`参数错误: ${errors}`)
          } else {
            ElMessage.error(data.message || '参数错误')
          }
          break

        case 429:
          ElMessage.error('请求过于频繁，请稍后再试')
          break

        case 500:
          ElMessage.error('服务器内部错误')
          break

        default:
          ElMessage.error(data?.message || `请求失败 (${status})`)
      }
    } else if (error.code === 'ECONNABORTED') {
      ElMessage.error('请求超时，请检查网络连接')
    } else if (error.message === 'Network Error') {
      ElMessage.error('网络连接失败，请检查网络')
    } else {
      ElMessage.error('请求失败，请稍后重试')
    }

    return Promise.reject(error)
  }
)

// 生成请求ID
function generateRequestId() {
  return Date.now().toString(36) + Math.random().toString(36).substr(2)
}

// 通用API方法
export const apiRequest = {
  get: (url, params = {}) => api.get(url, { params }),
  post: (url, data = {}) => api.post(url, data),
  put: (url, data = {}) => api.put(url, data),
  delete: (url) => api.delete(url),
  patch: (url, data = {}) => api.patch(url, data)
}

// 认证相关API
export const authAPI = {
  // 用户注册
  register: (data) => api.post('/auth/register', data),

  // 用户登录
  login: (data) => api.post('/auth/login', data),

  // 刷新token
  refreshToken: (refreshToken) => api.post('/auth/refresh', { refresh_token: refreshToken }),

  // 获取当前用户信息
  getCurrentUser: () => api.get('/auth/me'),

  // 更新用户信息
  updateUser: (data) => api.put('/auth/me', data),

  // 修改密码
  changePassword: (data) => api.post('/auth/change-password', data),

  // 抖音认证
  douyinAuth: (data) => api.post('/auth/douyin-auth', data),

  // 获取抖音登录二维码
  getQRCode: () => api.get('/auth/douyin-qrcode'),

  // 检查二维码状态
  checkQRStatus: (token) => api.get(`/auth/douyin-qrcode/check?token=${token}`),

  // 登出
  logout: () => api.post('/auth/logout')
}

// 抖音相关API
export const douyinAPI = {
  // 获取视频信息
  getVideoInfo: (awemeId) => api.get(`/douyin/video/${awemeId}`),

  // 解析分享链接
  parseShareUrl: (url) => api.post('/douyin/parse', { url }),

  // 获取用户信息
  getUserInfo: (secUid) => api.get(`/douyin/user/${secUid}`),

  // 获取用户作品
  getUserPosts: (secUid, params = {}) => api.get(`/douyin/user/${secUid}/posts`, { params }),

  // 获取用户喜欢
  getUserLikes: (secUid, params = {}) => api.get(`/douyin/user/${secUid}/likes`, { params }),

  // 获取历史记录
  getHistory: (params = {}) => api.get('/douyin/history', { params }),

  // 同步历史记录
  syncHistory: () => api.post('/douyin/history/sync'),

  // 获取视频评论
  getComments: (awemeId, params = {}) => api.get(`/douyin/video/${awemeId}/comments`, { params }),

  // 搜索视频
  searchVideos: (params = {}) => api.get('/douyin/search', { params }),

  // 获取收藏夹
  getCollections: (params = {}) => api.get('/douyin/collections', { params }),

  // 获取收藏夹内容
  getCollectionItems: (collectionId, params = {}) => api.get(`/douyin/collections/${collectionId}/items`, { params })
}

// 下载相关API
export const downloadAPI = {
  // 创建下载任务
  createTask: (data) => api.post('/download/tasks', data),

  // 获取下载任务列表
  getTasks: (params = {}) => api.get('/download/tasks', { params }),

  // 获取下载任务详情
  getTask: (taskId) => api.get(`/download/tasks/${taskId}`),

  // 取消下载任务
  cancelTask: (taskId) => api.delete(`/download/tasks/${taskId}`),

  // 重试下载任务
  retryTask: (taskId) => api.post(`/download/tasks/${taskId}/retry`),

  // 批量下载
  batchDownload: (data) => api.post('/download/batch', data),

  // 下载用户所有作品
  downloadUserPosts: (secUid, data = {}) => api.post(`/download/user/${secUid}/posts`, data),

  // 下载收藏夹
  downloadCollection: (collectionId, data = {}) => api.post(`/download/collection/${collectionId}`, data)
}

// 分析相关API
export const analysisAPI = {
  // 获取用户统计
  getUserStats: (params = {}) => api.get('/analysis/stats', { params }),

  // 获取年度总结
  getAnnualSummary: (year) => api.get(`/analysis/annual/${year}`),

  // 生成年度总结
  generateAnnualSummary: (year) => api.post(`/analysis/annual/${year}/generate`),

  // 获取观看趋势
  getWatchTrends: (params = {}) => api.get('/analysis/trends', { params }),

  // 获取内容偏好
  getContentPreferences: (params = {}) => api.get('/analysis/preferences', { params }),

  // 获取时间分布
  getTimeDistribution: (params = {}) => api.get('/analysis/time-distribution', { params }),

  // AI分析视频
  analyzeVideo: (awemeId) => api.post(`/analysis/video/${awemeId}/analyze`),

  // 获取推荐内容
  getRecommendations: (params = {}) => api.get('/analysis/recommendations', { params })
}

// 任务相关API
export const taskAPI = {
  // 获取任务列表
  getTasks: (params = {}) => api.get('/tasks', { params }),

  // 获取任务详情
  getTask: (taskId) => api.get(`/tasks/${taskId}`),

  // 创建任务
  createTask: (data) => api.post('/tasks', data),

  // 取消任务
  cancelTask: (taskId) => api.delete(`/tasks/${taskId}`),

  // 获取任务日志
  getTaskLogs: (taskId) => api.get(`/tasks/${taskId}/logs`)
}

// 用户资料相关API
export const profileAPI = {
  // 获取用户资料
  getProfile: () => api.get('/profile'),

  // 更新用户资料
  updateProfile: (data) => api.put('/profile', data),

  // 上传头像
  uploadAvatar: (file) => {
    const formData = new FormData()
    formData.append('avatar', file)
    return api.post('/profile/avatar', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },

  // 获取用户活动记录
  getActivityLog: (params = {}) => api.get('/profile/activity', { params }),

  // 获取用户统计信息
  getStats: () => api.get('/profile/stats')
}

// 设置相关API
export const settingsAPI = {
  // 获取用户设置
  getSettings: () => api.get('/settings'),

  // 更新用户设置
  updateSettings: (data) => api.put('/settings', data),

  // 获取抖音绑定状态
  getDouyinBinding: () => api.get('/settings/douyin-binding'),

  // 绑定抖音账号
  bindDouyin: (data) => api.post('/settings/bind-douyin', data),

  // 解绑抖音账号
  unbindDouyin: () => api.delete('/settings/douyin-binding'),

  // 获取通知设置
  getNotificationSettings: () => api.get('/settings/notifications'),

  // 更新通知设置
  updateNotificationSettings: (data) => api.put('/settings/notifications', data),

  // 获取隐私设置
  getPrivacySettings: () => api.get('/settings/privacy'),

  // 更新隐私设置
  updatePrivacySettings: (data) => api.put('/settings/privacy', data),

  // 导出用户数据
  exportData: () => api.post('/settings/export-data'),

  // 删除账户
  deleteAccount: (password) => api.delete('/settings/account', { data: { password } })
}

export default api
