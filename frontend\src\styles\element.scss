// Element Plus 自定义样式

// 全局主题色覆盖
:root {
  --el-color-primary: #{$primary-color};
  --el-color-primary-light-3: #{lighten($primary-color, 30%)};
  --el-color-primary-light-5: #{lighten($primary-color, 50%)};
  --el-color-primary-light-7: #{lighten($primary-color, 70%)};
  --el-color-primary-light-8: #{lighten($primary-color, 80%)};
  --el-color-primary-light-9: #{lighten($primary-color, 90%)};
  --el-color-primary-dark-2: #{darken($primary-color, 20%)};
}

// 按钮自定义
.el-button {
  border-radius: 6px;
  font-weight: 500;
  
  &.el-button--primary {
    background-color: $primary-color;
    border-color: $primary-color;
    
    &:hover {
      background-color: darken($primary-color, 10%);
      border-color: darken($primary-color, 10%);
    }
  }
}

// 卡片自定义
.el-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: none;
  
  .el-card__header {
    padding: 16px 20px;
    border-bottom: 1px solid #f0f0f0;
    background-color: #fafafa;
  }
  
  .el-card__body {
    padding: 20px;
  }
}

// 表格自定义
.el-table {
  .el-table__header {
    th {
      background-color: #fafafa;
      color: #606266;
      font-weight: 600;
    }
  }
  
  .el-table__row {
    &:hover {
      background-color: #f5f7fa;
    }
  }
}

// 分页自定义
.el-pagination {
  .el-pager li {
    &.is-active {
      background-color: $primary-color;
      color: #fff;
    }
  }
  
  .btn-next,
  .btn-prev {
    &:hover {
      color: $primary-color;
    }
  }
}

// 输入框自定义
.el-input {
  .el-input__wrapper {
    border-radius: 6px;
    
    &:hover {
      box-shadow: 0 0 0 1px $primary-color inset;
    }
    
    &.is-focus {
      box-shadow: 0 0 0 1px $primary-color inset;
    }
  }
}

// 选择器自定义
.el-select {
  .el-select__wrapper {
    border-radius: 6px;
    
    &:hover {
      box-shadow: 0 0 0 1px $primary-color inset;
    }
    
    &.is-focus {
      box-shadow: 0 0 0 1px $primary-color inset;
    }
  }
}

// 对话框自定义
.el-dialog {
  border-radius: 8px;
  
  .el-dialog__header {
    padding: 20px 20px 10px;
    
    .el-dialog__title {
      font-size: 18px;
      font-weight: 600;
      color: #303133;
    }
  }
  
  .el-dialog__body {
    padding: 10px 20px 20px;
  }
  
  .el-dialog__footer {
    padding: 10px 20px 20px;
  }
}

// 消息提示自定义
.el-message {
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

// 通知自定义
.el-notification {
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

// 菜单自定义
.el-menu {
  border-right: none;
  
  .el-menu-item {
    &:hover {
      background-color: lighten($primary-color, 45%);
      color: $primary-color;
    }
    
    &.is-active {
      background-color: lighten($primary-color, 40%);
      color: $primary-color;
      font-weight: 600;
    }
  }
  
  .el-sub-menu__title {
    &:hover {
      background-color: lighten($primary-color, 45%);
      color: $primary-color;
    }
  }
}

// 标签页自定义
.el-tabs {
  .el-tabs__nav-wrap::after {
    background-color: #e4e7ed;
  }
  
  .el-tabs__item {
    &:hover {
      color: $primary-color;
    }
    
    &.is-active {
      color: $primary-color;
    }
  }
  
  .el-tabs__active-bar {
    background-color: $primary-color;
  }
}

// 步骤条自定义
.el-steps {
  .el-step__icon {
    &.is-process {
      background-color: $primary-color;
      border-color: $primary-color;
    }
  }
  
  .el-step__title {
    &.is-process {
      color: $primary-color;
    }
  }
}

// 加载自定义
.el-loading-mask {
  background-color: rgba(255, 255, 255, 0.9);
}

// 骨架屏自定义
.el-skeleton {
  .el-skeleton__item {
    background: linear-gradient(90deg, #f2f2f2 25%, #e6e6e6 37%, #f2f2f2 63%);
    background-size: 400% 100%;
    animation: el-skeleton-loading 1.4s ease infinite;
  }
}
