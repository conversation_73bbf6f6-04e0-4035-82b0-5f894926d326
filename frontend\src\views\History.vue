<template>
  <div class="history-container">
    <!-- 搜索和筛选区域 -->
    <div class="search-section">
      <el-card class="search-card">
        <div class="search-content">
          <div class="search-bar">
            <el-input
              v-model="searchKeyword"
              placeholder="搜索视频标题、作者..."
              clearable
              @keyup.enter="handleSearch"
              class="search-input"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
              <template #append>
                <el-button @click="handleSearch" :loading="douyinStore.loading">
                  搜索
                </el-button>
              </template>
            </el-input>
          </div>

          <div class="filter-bar">
            <el-select v-model="dateRange" placeholder="时间范围" clearable @change="handleFilter">
              <el-option label="今天" value="today" />
              <el-option label="最近7天" value="week" />
              <el-option label="最近30天" value="month" />
              <el-option label="最近90天" value="quarter" />
            </el-select>

            <el-select v-model="sortBy" placeholder="排序方式" @change="handleFilter">
              <el-option label="观看时间" value="watch_time" />
              <el-option label="视频时长" value="duration" />
              <el-option label="点赞数" value="like_count" />
              <el-option label="播放量" value="play_count" />
            </el-select>

            <el-select v-model="sortOrder" placeholder="排序顺序" @change="handleFilter">
              <el-option label="降序" value="desc" />
              <el-option label="升序" value="asc" />
            </el-select>

            <el-button @click="handleReset" type="info" plain>
              <el-icon><Refresh /></el-icon>
              重置
            </el-button>

            <el-button @click="handleSync" type="primary" :loading="douyinStore.syncing">
              <el-icon><Refresh /></el-icon>
              同步历史
            </el-button>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 统计信息 -->
    <div class="stats-section">
      <el-row :gutter="16">
        <el-col :xs="12" :sm="6" :md="6" :lg="6">
          <div class="stat-item">
            <div class="stat-icon total">
              <el-icon><VideoPlay /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ filteredHistory.length }}</div>
              <div class="stat-label">总视频数</div>
            </div>
          </div>
        </el-col>

        <el-col :xs="12" :sm="6" :md="6" :lg="6">
          <div class="stat-item">
            <div class="stat-icon duration">
              <el-icon><Timer /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ formatDuration(totalDuration) }}</div>
              <div class="stat-label">总时长</div>
            </div>
          </div>
        </el-col>

        <el-col :xs="12" :sm="6" :md="6" :lg="6">
          <div class="stat-item">
            <div class="stat-icon likes">
              <el-icon><Star /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ formatCount(totalLikes) }}</div>
              <div class="stat-label">总点赞</div>
            </div>
          </div>
        </el-col>

        <el-col :xs="12" :sm="6" :md="6" :lg="6">
          <div class="stat-item">
            <div class="stat-icon authors">
              <el-icon><User /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ uniqueAuthors.length }}</div>
              <div class="stat-label">关注作者</div>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 视频列表 -->
    <div class="video-list-section">
      <div class="list-header">
        <div class="view-toggle">
          <el-radio-group v-model="viewMode" @change="handleViewModeChange">
            <el-radio-button label="grid">
              <el-icon><Grid /></el-icon>
              网格
            </el-radio-button>
            <el-radio-button label="list">
              <el-icon><List /></el-icon>
              列表
            </el-radio-button>
          </el-radio-group>
        </div>

        <div class="batch-actions">
          <el-checkbox v-model="selectAll" @change="handleSelectAll" :indeterminate="isIndeterminate">
            全选
          </el-checkbox>
          <el-button
            v-if="selectedVideos.length > 0"
            @click="handleBatchDownload"
            type="primary"
            size="small"
          >
            <el-icon><Download /></el-icon>
            批量下载 ({{ selectedVideos.length }})
          </el-button>
          <el-button
            v-if="selectedVideos.length > 0"
            @click="handleBatchAnalyze"
            type="success"
            size="small"
          >
            <el-icon><TrendCharts /></el-icon>
            批量分析
          </el-button>
        </div>
      </div>

      <!-- 网格视图 -->
      <div v-if="viewMode === 'grid'" class="grid-view">
        <el-row :gutter="16">
          <el-col
            v-for="item in paginatedHistory"
            :key="item.id"
            :xs="24"
            :sm="12"
            :md="8"
            :lg="6"
          >
            <div class="video-card" @click="handleVideoClick(item)">
              <div class="video-checkbox">
                <el-checkbox
                  v-model="selectedVideos"
                  :label="item.id"
                  @click.stop
                />
              </div>

              <div class="video-cover">
                <img :src="item.cover_url || '/default-cover.jpg'" :alt="item.video_title" />
                <div class="video-duration">{{ formatShortDuration(item.duration) }}</div>
                <div class="video-overlay">
                  <el-button @click.stop="handleDownload(item)" type="primary" circle>
                    <el-icon><Download /></el-icon>
                  </el-button>
                  <el-button @click.stop="handleAnalyze(item)" type="success" circle>
                    <el-icon><TrendCharts /></el-icon>
                  </el-button>
                </div>
              </div>

              <div class="video-info">
                <div class="video-title">{{ item.video_title || '无标题' }}</div>
                <div class="video-meta">
                  <div class="author">
                    <img :src="item.author_avatar || '/default-avatar.jpg'" alt="作者头像" class="author-avatar" />
                    <span>{{ item.author_nickname }}</span>
                  </div>
                  <div class="stats">
                    <span class="stat-item">
                      <el-icon><View /></el-icon>
                      {{ formatCount(item.play_count) }}
                    </span>
                    <span class="stat-item">
                      <el-icon><Star /></el-icon>
                      {{ formatCount(item.like_count) }}
                    </span>
                  </div>
                </div>
                <div class="video-time">{{ formatTime(item.watch_time) }}</div>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 列表视图 -->
      <div v-else class="list-view">
        <el-table
          :data="paginatedHistory"
          @selection-change="handleSelectionChange"
          row-key="id"
          class="video-table"
        >
          <el-table-column type="selection" width="55" />

          <el-table-column label="视频" width="300">
            <template #default="{ row }">
              <div class="table-video-info">
                <img :src="row.cover_url || '/default-cover.jpg'" alt="封面" class="table-cover" />
                <div class="table-details">
                  <div class="table-title">{{ row.video_title || '无标题' }}</div>
                  <div class="table-duration">{{ formatShortDuration(row.duration) }}</div>
                </div>
              </div>
            </template>
          </el-table-column>

          <el-table-column label="作者" width="150">
            <template #default="{ row }">
              <div class="table-author">
                <img :src="row.author_avatar || '/default-avatar.jpg'" alt="头像" class="table-avatar" />
                <span>{{ row.author_nickname }}</span>
              </div>
            </template>
          </el-table-column>

          <el-table-column label="数据" width="120">
            <template #default="{ row }">
              <div class="table-stats">
                <div>播放: {{ formatCount(row.play_count) }}</div>
                <div>点赞: {{ formatCount(row.like_count) }}</div>
              </div>
            </template>
          </el-table-column>

          <el-table-column label="观看时间" width="150">
            <template #default="{ row }">
              {{ formatTime(row.watch_time) }}
            </template>
          </el-table-column>

          <el-table-column label="操作" width="150">
            <template #default="{ row }">
              <el-button @click="handleDownload(row)" type="primary" size="small">
                <el-icon><Download /></el-icon>
              </el-button>
              <el-button @click="handleAnalyze(row)" type="success" size="small">
                <el-icon><TrendCharts /></el-icon>
              </el-button>
              <el-button @click="handleVideoClick(row)" type="info" size="small">
                <el-icon><View /></el-icon>
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 空状态 -->
      <div v-if="filteredHistory.length === 0" class="empty-state">
        <el-empty description="暂无历史记录">
          <el-button type="primary" @click="handleSync">
            <el-icon><Refresh /></el-icon>
            同步历史记录
          </el-button>
        </el-empty>
      </div>

      <!-- 分页 -->
      <div v-if="filteredHistory.length > 0" class="pagination-section">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[20, 50, 100, 200]"
          :total="filteredHistory.length"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
  </div>

    <!-- 视频详情弹窗 -->
    <el-dialog
      v-model="showVideoDialog"
      title="视频详情"
      width="800px"
      :before-close="handleCloseDialog"
      class="video-dialog"
    >
      <div v-if="selectedVideo" class="video-detail">
        <div class="detail-cover">
          <img :src="selectedVideo.cover_url" alt="视频封面" />
        </div>
        <div class="detail-info">
          <h3>{{ selectedVideo.video_title || selectedVideo.desc }}</h3>
          <div class="detail-meta">
            <div class="meta-item">
              <span class="meta-label">作者：</span>
              <span>{{ selectedVideo.author_nickname }}</span>
            </div>
            <div class="meta-item">
              <span class="meta-label">时长：</span>
              <span>{{ formatDuration(selectedVideo.duration) }}</span>
            </div>
            <div class="meta-item">
              <span class="meta-label">播放量：</span>
              <span>{{ formatCount(selectedVideo.play_count) }}</span>
            </div>
            <div class="meta-item">
              <span class="meta-label">点赞数：</span>
              <span>{{ formatCount(selectedVideo.like_count) }}</span>
            </div>
            <div class="meta-item">
              <span class="meta-label">评论数：</span>
              <span>{{ formatCount(selectedVideo.comment_count) }}</span>
            </div>
            <div class="meta-item">
              <span class="meta-label">观看时间：</span>
              <span>{{ formatTime(selectedVideo.watch_time) }}</span>
            </div>
          </div>
          <div v-if="selectedVideo.desc" class="detail-desc">
            <h4>视频描述</h4>
            <p>{{ selectedVideo.desc }}</p>
          </div>
        </div>
      </div>
      <template #footer>
        <el-button @click="showVideoDialog = false">关闭</el-button>
        <el-button type="success" @click="handleAnalyze(selectedVideo)">
          <el-icon><TrendCharts /></el-icon>
          AI分析
        </el-button>
        <el-button type="primary" @click="handleDownload(selectedVideo)">
          <el-icon><Download /></el-icon>
          下载视频
        </el-button>
      </template>
    </el-dialog>

</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useDouyinStore } from '@/stores/douyin'
import { ElMessage, ElMessageBox } from 'element-plus'
import dayjs from 'dayjs'

const router = useRouter()
const douyinStore = useDouyinStore()

// 响应式数据
const searchKeyword = ref('')
const dateRange = ref('')
const sortBy = ref('watch_time')
const sortOrder = ref('desc')
const viewMode = ref('grid')
const currentPage = ref(1)
const pageSize = ref(20)
const selectedVideos = ref([])
const selectAll = ref(false)
const showVideoDialog = ref(false)
const selectedVideo = ref(null)

// 计算属性
const filteredHistory = computed(() => {
  let result = [...douyinStore.history]

  // 搜索过滤
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    result = result.filter(item =>
      (item.video_title && item.video_title.toLowerCase().includes(keyword)) ||
      (item.author_nickname && item.author_nickname.toLowerCase().includes(keyword))
    )
  }

  // 时间范围过滤
  if (dateRange.value) {
    const now = dayjs()
    let startDate

    switch (dateRange.value) {
      case 'today':
        startDate = now.startOf('day')
        break
      case 'week':
        startDate = now.subtract(7, 'day')
        break
      case 'month':
        startDate = now.subtract(30, 'day')
        break
      case 'quarter':
        startDate = now.subtract(90, 'day')
        break
    }

    if (startDate) {
      result = result.filter(item => dayjs(item.watch_time).isAfter(startDate))
    }
  }

  // 排序
  result.sort((a, b) => {
    let aValue = a[sortBy.value] || 0
    let bValue = b[sortBy.value] || 0

    if (sortBy.value === 'watch_time') {
      aValue = new Date(aValue).getTime()
      bValue = new Date(bValue).getTime()
    }

    return sortOrder.value === 'desc' ? bValue - aValue : aValue - bValue
  })

  return result
})

const paginatedHistory = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return filteredHistory.value.slice(start, end)
})

const totalDuration = computed(() => {
  return filteredHistory.value.reduce((total, item) => total + (item.duration || 0), 0)
})

const totalLikes = computed(() => {
  return filteredHistory.value.reduce((total, item) => total + (item.like_count || 0), 0)
})

const uniqueAuthors = computed(() => {
  const authors = new Set()
  filteredHistory.value.forEach(item => {
    if (item.author_nickname) {
      authors.add(item.author_nickname)
    }
  })
  return Array.from(authors)
})

const isIndeterminate = computed(() => {
  return selectedVideos.value.length > 0 && selectedVideos.value.length < paginatedHistory.value.length
})

// 格式化函数
const formatDuration = (seconds) => {
  if (!seconds) return '0分钟'
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)

  if (hours > 0) {
    return `${hours}小时${minutes}分钟`
  }
  return `${minutes}分钟`
}

const formatShortDuration = (seconds) => {
  if (!seconds) return '00:00'
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const secs = Math.floor(seconds % 60)

  if (hours > 0) {
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
  }
  return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
}

const formatCount = (count) => {
  if (!count) return '0'
  if (count >= 10000) {
    return (count / 10000).toFixed(1) + 'w'
  }
  if (count >= 1000) {
    return (count / 1000).toFixed(1) + 'k'
  }
  return count.toString()
}

const formatTime = (time) => {
  return dayjs(time).format('YYYY-MM-DD HH:mm')
}

// 事件处理
const handleSearch = () => {
  currentPage.value = 1
}

const handleFilter = () => {
  currentPage.value = 1
}

const handleReset = () => {
  searchKeyword.value = ''
  dateRange.value = ''
  sortBy.value = 'watch_time'
  sortOrder.value = 'desc'
  currentPage.value = 1
}

const handleSync = async () => {
  await douyinStore.syncHistory()
}

const handleViewModeChange = () => {
  selectedVideos.value = []
  selectAll.value = false
}

const handleSelectAll = (value) => {
  if (value) {
    selectedVideos.value = paginatedHistory.value.map(item => item.id)
  } else {
    selectedVideos.value = []
  }
}

const handleSelectionChange = (selection) => {
  selectedVideos.value = selection.map(item => item.id)
  selectAll.value = selection.length === paginatedHistory.value.length
}

const handleVideoClick = (video) => {
  selectedVideo.value = video
  showVideoDialog.value = true
}

const handleDownload = (video) => {
  router.push({
    path: '/download',
    query: { video_id: video.id }
  })
}

const handleAnalyze = async (video) => {
  try {
    await douyinStore.analyzeVideo(video.aweme_id || video.id)
    router.push({
      path: '/analysis',
      query: { video_id: video.id }
    })
  } catch (error) {
    ElMessage.error('分析失败')
  }
}

const handleBatchDownload = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要下载选中的 ${selectedVideos.value.length} 个视频吗？`,
      '批量下载',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    router.push({
      path: '/download',
      query: { video_ids: selectedVideos.value.join(',') }
    })
  } catch {
    // 用户取消
  }
}

const handleBatchAnalyze = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要分析选中的 ${selectedVideos.value.length} 个视频吗？`,
      '批量分析',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    ElMessage.info('正在批量分析，请稍候...')
    // 这里可以添加批量分析的逻辑
  } catch {
    // 用户取消
  }
}

const handleSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1
}

const handleCurrentChange = (page) => {
  currentPage.value = page
}

const handleCloseDialog = () => {
  showVideoDialog.value = false
  selectedVideo.value = null
}

// 监听分页数据变化，更新选择状态
watch(paginatedHistory, () => {
  selectedVideos.value = []
  selectAll.value = false
})

// 组件挂载时加载数据
onMounted(async () => {
  if (douyinStore.history.length === 0) {
    await douyinStore.getHistory()
  }
})
</script>

<style lang="scss" scoped>
.history-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 16px;
}

.search-section {
  margin-bottom: 24px;
  
  .search-card {
    border: none;
    border-radius: var(--border-radius-large);
    box-shadow: var(--shadow-light);
    overflow: hidden;
    
    :deep(.el-card__body) {
      padding: 20px;
    }
    
    .search-content {
      .search-bar {
        margin-bottom: 16px;
        
        .search-input {
          :deep(.el-input__wrapper) {
            border-radius: var(--border-radius-base);
          }
          
          :deep(.el-input-group__append) {
            button {
              background-color: var(--primary-color);
              border-color: var(--primary-color);
              color: white;
              
              &:hover {
                background-color: var(--primary-light);
                border-color: var(--primary-light);
              }
            }
          }
        }
      }
      
      .filter-bar {
        display: flex;
        flex-wrap: wrap;
        gap: 12px;
        align-items: center;
        
        .el-select {
          width: 140px;
        }
      }
    }
  }
}

.stats-section {
  margin-bottom: 24px;
  
  .stat-item {
    background-color: var(--bg-white);
    border-radius: var(--border-radius-large);
    padding: 16px;
    box-shadow: var(--shadow-light);
    display: flex;
    align-items: center;
    gap: 16px;
    transition: transform 0.2s;
    height: 100%;
    
    &:hover {
      transform: translateY(-5px);
    }
    
    .stat-icon {
      width: 48px;
      height: 48px;
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 24px;
      color: white;
      
      &.total {
        background: linear-gradient(135deg, var(--primary-color), #ff5050);
      }
      
      &.duration {
        background: linear-gradient(135deg, #ff9a9e, #fad0c4);
      }
      
      &.likes {
        background: linear-gradient(135deg, #a18cd1, #fbc2eb);
      }
      
      &.authors {
        background: linear-gradient(135deg, #84fab0, #8fd3f4);
      }
    }
    
    .stat-info {
      flex: 1;
      
      .stat-value {
        font-size: 24px;
        font-weight: 600;
        color: var(--text-primary);
        line-height: 1;
      }
      
      .stat-label {
        font-size: 12px;
        color: var(--text-secondary);
        margin-top: 4px;
      }
    }
  }
}

.video-list-section {
  .list-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    flex-wrap: wrap;
    gap: 16px;
    
    .view-toggle {
      :deep(.el-radio-button__inner) {
        border-radius: 20px;
        padding: 8px 16px;
      }
      
      :deep(.el-radio-button:first-child .el-radio-button__inner) {
        border-radius: 20px 0 0 20px;
      }
      
      :deep(.el-radio-button:last-child .el-radio-button__inner) {
        border-radius: 0 20px 20px 0;
      }
      
      :deep(.el-radio-button__original) {
        opacity: 0;
      }
      
      :deep(.el-radio-button__inner:hover) {
        color: var(--primary-color);
      }
      
      :deep(.el-radio-button__orig-radio:checked + .el-radio-button__inner) {
        background-color: var(--primary-color);
        border-color: var(--primary-color);
        box-shadow: -1px 0 0 0 var(--primary-color);
      }
    }
    
    .batch-actions {
      display: flex;
      align-items: center;
      gap: 12px;
    }
  }
  
  .grid-view {
    .video-card {
      position: relative;
      margin-bottom: 24px;
      cursor: pointer;
      transition: transform 0.2s;
      
      &:hover {
        transform: translateY(-5px);
        
        .video-cover {
          .video-overlay {
            opacity: 1;
          }
        }
      }
      
      .video-checkbox {
        position: absolute;
        top: 8px;
        left: 8px;
        z-index: 2;
      }
      
      .video-cover {
        position: relative;
        border-radius: var(--border-radius-large);
        overflow: hidden;
        padding-bottom: 133.33%; // 3:4 比例
        background-color: #f0f0f0;
        
        img {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
        
        .video-duration {
          position: absolute;
          bottom: 8px;
          right: 8px;
          background-color: rgba(0, 0, 0, 0.6);
          color: white;
          padding: 2px 6px;
          border-radius: 4px;
          font-size: 12px;
        }
        
        .video-overlay {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 16px;
          background-color: rgba(0, 0, 0, 0.3);
          opacity: 0;
          transition: opacity 0.2s;
          
          .el-button {
            --el-button-size: 40px;
            --el-button-text-color: white;
            --el-button-hover-text-color: white;
            
            &.el-button--primary {
              --el-button-bg-color: var(--primary-color);
              --el-button-border-color: var(--primary-color);
              --el-button-hover-bg-color: var(--primary-light);
              --el-button-hover-border-color: var(--primary-light);
            }
          }
        }
      }
      
      .video-info {
        padding: 12px 0;
        
        .video-title {
          font-size: 14px;
          font-weight: 500;
          color: var(--text-primary);
          margin-bottom: 8px;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          line-height: 1.4;
        }
        
        .video-meta {
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-bottom: 8px;
          
          .author {
            display: flex;
            align-items: center;
            gap: 6px;
            font-size: 12px;
            color: var(--text-secondary);
            
            .author-avatar {
              width: 20px;
              height: 20px;
              border-radius: 50%;
              object-fit: cover;
            }
          }
          
          .stats {
            display: flex;
            gap: 8px;
            
            .stat-item {
              display: flex;
              align-items: center;
              gap: 4px;
              font-size: 12px;
              color: var(--text-secondary);
              
              .el-icon {
                font-size: 14px;
              }
            }
          }
        }
        
        .video-time {
          font-size: 12px;
          color: var(--text-placeholder);
        }
      }
    }
  }
  
  .list-view {
    .video-table {
      border-radius: var(--border-radius-large);
      overflow: hidden;
      
      :deep(.el-table__header) {
        th {
          background-color: var(--bg-white);
          color: var(--text-primary);
          font-weight: 500;
        }
      }
      
      :deep(.el-table__row) {
        transition: background-color 0.2s;
        
        &:hover {
          background-color: var(--border-lighter);
        }
      }
      
      .table-video-info {
        display: flex;
        align-items: center;
        gap: 12px;
        
        .table-cover {
          width: 80px;
          height: 45px;
          border-radius: var(--border-radius-base);
          object-fit: cover;
        }
        
        .table-details {
          .table-title {
            font-size: 14px;
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: 4px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            max-width: 180px;
          }
          
          .table-duration {
            font-size: 12px;
            color: var(--text-secondary);
          }
        }
      }
      
      .table-author {
        display: flex;
        align-items: center;
        gap: 8px;
        
        .table-avatar {
          width: 24px;
          height: 24px;
          border-radius: 50%;
          object-fit: cover;
        }
      }
      
      .table-stats {
        font-size: 12px;
        color: var(--text-secondary);
        
        div {
          margin-bottom: 4px;
          
          &:last-child {
            margin-bottom: 0;
          }
        }
      }
      
      .table-actions {
        .el-button {
          padding: 6px;
          
          & + .el-button {
            margin-left: 4px;
          }
        }
      }
    }
  }
}

.pagination-section {
  display: flex;
  justify-content: center;
  margin-top: 24px;
}

// 响应式
@media (max-width: 768px) {
  .filter-bar {
    flex-direction: column;
    align-items: stretch !important;
    
    .el-select {
      width: 100% !important;
    }
  }
  
  .list-header {
    flex-direction: column;
    align-items: stretch !important;
    
    .view-toggle, .batch-actions {
      width: 100%;
    }
  }
  
  .video-table {
    .table-video-info {
      .table-details {
        .table-title {
          max-width: 120px;
        }
      }
    }
  }
}

// 暗色主题适配
.dark {
  .video-card {
    .video-cover {
      background-color: #2a2a2a;
    }
  }
}
</style>
