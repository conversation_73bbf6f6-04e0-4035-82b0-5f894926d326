from pydantic import BaseModel, EmailStr, validator
from typing import Optional, Dict, Any, List
from datetime import datetime


class UserBase(BaseModel):
    """用户基础模式"""
    username: str
    email: Optional[EmailStr] = None
    full_name: Optional[str] = None
    avatar_url: Optional[str] = None
    douyin_nickname: Optional[str] = None


class UserCreate(UserBase):
    """用户创建模式"""
    password: str
    
    @validator('password')
    def validate_password(cls, v):
        if len(v) < 6:
            raise ValueError('密码长度至少6位')
        return v


class UserUpdate(BaseModel):
    """用户更新模式"""
    email: Optional[EmailStr] = None
    full_name: Optional[str] = None
    avatar_url: Optional[str] = None
    douyin_nickname: Optional[str] = None
    douyin_cookie: Optional[str] = None
    settings: Optional[Dict[str, Any]] = None


class UserInDB(UserBase):
    """数据库中的用户模式"""
    id: int
    douyin_uid: Optional[str] = None
    douyin_sec_uid: Optional[str] = None
    douyin_open_id: Optional[str] = None
    douyin_access_token: Optional[str] = None
    douyin_refresh_token: Optional[str] = None
    douyin_expires_in: Optional[int] = None
    is_active: bool = True
    is_verified: bool = False
    is_premium: bool = False
    created_at: datetime
    updated_at: Optional[datetime] = None
    last_login: Optional[datetime] = None
    settings: Optional[Dict[str, Any]] = None
    
    class Config:
        from_attributes = True


class User(UserInDB):
    """用户响应模式"""
    douyin_info: Optional[Dict[str, Any]] = None


class UserLogin(BaseModel):
    """用户登录模式"""
    username: str
    password: str


class UserRegister(UserCreate):
    """用户注册模式"""
    confirm_password: str
    
    @validator('confirm_password')
    def passwords_match(cls, v, values):
        if 'password' in values and v != values['password']:
            raise ValueError('两次输入的密码不一致')
        return v


class Token(BaseModel):
    """令牌模式"""
    access_token: str
    refresh_token: str
    token_type: str = "bearer"
    expires_in: int


class TokenData(BaseModel):
    """令牌数据模式"""
    username: Optional[str] = None
    user_id: Optional[int] = None


class PasswordChange(BaseModel):
    """密码修改模式"""
    current_password: str
    new_password: str
    confirm_password: str
    
    @validator('new_password')
    def validate_new_password(cls, v):
        if len(v) < 6:
            raise ValueError('新密码长度至少6位')
        return v
    
    @validator('confirm_password')
    def passwords_match(cls, v, values):
        if 'new_password' in values and v != values['new_password']:
            raise ValueError('两次输入的新密码不一致')
        return v


class DouyinAuth(BaseModel):
    """抖音认证模式"""
    douyin_uid: Optional[str] = None
    douyin_sec_uid: Optional[str] = None
    douyin_cookie: Optional[str] = None
    douyin_nickname: Optional[str] = None
    douyin_open_id: Optional[str] = None
    access_token: Optional[str] = None
    refresh_token: Optional[str] = None
    expires_in: Optional[int] = None
    user_info: Optional[Dict[str, Any]] = None


class DouyinQRLogin(BaseModel):
    """抖音二维码登录模式"""
    access_token: str
    refresh_token: str
    open_id: str
    expires_in: int
    user_info: Dict[str, Any]


class DouyinQRCode(BaseModel):
    """抖音二维码模式"""
    qr_code: str
    token: str
    expires_in: int


class DouyinQRStatus(BaseModel):
    """抖音二维码状态模式"""
    status: str
    user_info: Optional[Dict[str, Any]] = None


class UserStats(BaseModel):
    """用户统计模式"""
    total_videos: int = 0
    total_watch_time: float = 0.0
    total_likes: int = 0
    total_shares: int = 0
    total_comments: int = 0
    total_collections: int = 0
    total_downloads: int = 0
    
    class Config:
        from_attributes = True


class UserProfile(User):
    """用户详细资料模式"""
    stats: Optional[UserStats] = None
