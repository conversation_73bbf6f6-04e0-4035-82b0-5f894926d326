<template>
  <div class="profile-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h1>个人资料</h1>
        <p>管理您的个人信息和偏好设置</p>
      </div>
    </div>

    <!-- 用户信息卡片 -->
    <div class="profile-card">
      <el-card class="user-card">
        <div class="user-info">
          <div class="avatar-section">
            <div class="avatar-container">
              <img :src="userInfo.avatar || '/default-avatar.jpg'" alt="头像" class="user-avatar" />
              <div class="avatar-overlay">
                <el-button @click="handleAvatarUpload" type="primary" circle>
                  <el-icon><Camera /></el-icon>
                </el-button>
              </div>
            </div>
            <input ref="avatarInput" type="file" accept="image/*" @change="handleAvatarChange" style="display: none" />
          </div>

          <div class="user-details">
            <h2>{{ userInfo.username || '未设置用户名' }}</h2>
            <p class="user-email">{{ userInfo.email || '未设置邮箱' }}</p>
            <p class="user-join-date">加入时间：{{ formatTime(userInfo.created_at) }}</p>

            <div class="user-stats">
              <div class="stat-item">
                <span class="stat-value">{{ stats.totalVideos }}</span>
                <span class="stat-label">观看视频</span>
              </div>
              <div class="stat-item">
                <span class="stat-value">{{ formatDuration(stats.totalDuration) }}</span>
                <span class="stat-label">观看时长</span>
              </div>
              <div class="stat-item">
                <span class="stat-value">{{ stats.totalDownloads }}</span>
                <span class="stat-label">下载数量</span>
              </div>
            </div>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 编辑资料 -->
    <div class="edit-section">
      <el-card class="edit-card">
        <template #header>
          <span>编辑资料</span>
        </template>

        <el-form :model="editForm" :rules="formRules" ref="formRef" label-width="100px" class="edit-form">
          <el-form-item label="用户名" prop="username">
            <el-input v-model="editForm.username" placeholder="请输入用户名" />
          </el-form-item>

          <el-form-item label="邮箱" prop="email">
            <el-input v-model="editForm.email" placeholder="请输入邮箱" />
          </el-form-item>

          <el-form-item label="手机号" prop="phone">
            <el-input v-model="editForm.phone" placeholder="请输入手机号" />
          </el-form-item>

          <el-form-item label="个人简介">
            <el-input
              v-model="editForm.bio"
              type="textarea"
              :rows="4"
              placeholder="介绍一下自己吧..."
              maxlength="200"
              show-word-limit
            />
          </el-form-item>

          <el-form-item label="生日">
            <el-date-picker
              v-model="editForm.birthday"
              type="date"
              placeholder="选择生日"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
            />
          </el-form-item>

          <el-form-item label="性别">
            <el-radio-group v-model="editForm.gender">
              <el-radio label="male">男</el-radio>
              <el-radio label="female">女</el-radio>
              <el-radio label="other">其他</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item label="地区">
            <el-cascader
              v-model="editForm.location"
              :options="locationOptions"
              placeholder="选择地区"
              clearable
            />
          </el-form-item>

          <el-form-item>
            <el-button @click="handleSave" :loading="saving" type="primary">
              保存修改
            </el-button>
            <el-button @click="handleReset">
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </el-card>
    </div>

    <!-- 偏好设置 -->
    <div class="preferences-section">
      <el-card class="preferences-card">
        <template #header>
          <span>偏好设置</span>
        </template>

        <div class="preferences-content">
          <div class="preference-group">
            <h4>内容偏好</h4>
            <div class="preference-tags">
              <el-tag
                v-for="tag in contentTags"
                :key="tag.id"
                :type="tag.selected ? 'primary' : ''"
                :effect="tag.selected ? 'dark' : 'plain'"
                @click="toggleTag(tag)"
                class="preference-tag"
              >
                {{ tag.name }}
              </el-tag>
            </div>
          </div>

          <div class="preference-group">
            <h4>通知设置</h4>
            <div class="notification-settings">
              <div class="setting-item">
                <div class="setting-info">
                  <span class="setting-label">新视频推荐</span>
                  <span class="setting-desc">根据您的观看历史推荐新视频</span>
                </div>
                <el-switch v-model="preferences.videoRecommendation" />
              </div>

              <div class="setting-item">
                <div class="setting-info">
                  <span class="setting-label">下载完成通知</span>
                  <span class="setting-desc">视频下载完成时发送通知</span>
                </div>
                <el-switch v-model="preferences.downloadNotification" />
              </div>

              <div class="setting-item">
                <div class="setting-info">
                  <span class="setting-label">数据分析报告</span>
                  <span class="setting-desc">定期发送观看数据分析报告</span>
                </div>
                <el-switch v-model="preferences.analyticsReport" />
              </div>
            </div>
          </div>

          <div class="preference-group">
            <h4>隐私设置</h4>
            <div class="privacy-settings">
              <div class="setting-item">
                <div class="setting-info">
                  <span class="setting-label">公开观看历史</span>
                  <span class="setting-desc">允许其他用户查看您的观看历史</span>
                </div>
                <el-switch v-model="preferences.publicHistory" />
              </div>

              <div class="setting-item">
                <div class="setting-info">
                  <span class="setting-label">数据收集</span>
                  <span class="setting-desc">允许收集匿名使用数据以改进服务</span>
                </div>
                <el-switch v-model="preferences.dataCollection" />
              </div>
            </div>
          </div>

          <div class="preferences-actions">
            <el-button @click="handleSavePreferences" :loading="savingPreferences" type="primary">
              保存偏好设置
            </el-button>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 账号安全 -->
    <div class="security-section">
      <el-card class="security-card">
        <template #header>
          <span>账号安全</span>
        </template>

        <div class="security-content">
          <div class="security-item">
            <div class="security-info">
              <h4>修改密码</h4>
              <p>定期修改密码以保护账号安全</p>
            </div>
            <el-button @click="showPasswordDialog = true" type="primary">
              修改密码
            </el-button>
          </div>

          <div class="security-item">
            <div class="security-info">
              <h4>登录设备</h4>
              <p>查看和管理已登录的设备</p>
            </div>
            <el-button @click="handleViewDevices" type="info">
              查看设备
            </el-button>
          </div>

          <div class="security-item">
            <div class="security-info">
              <h4>注销账号</h4>
              <p>永久删除账号和所有相关数据</p>
            </div>
            <el-button @click="handleDeleteAccount" type="danger" plain>
              注销账号
            </el-button>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 修改密码弹窗 -->
    <el-dialog v-model="showPasswordDialog" title="修改密码" width="400px">
      <el-form :model="passwordForm" :rules="passwordRules" ref="passwordFormRef" label-width="100px">
        <el-form-item label="当前密码" prop="currentPassword">
          <el-input v-model="passwordForm.currentPassword" type="password" show-password />
        </el-form-item>

        <el-form-item label="新密码" prop="newPassword">
          <el-input v-model="passwordForm.newPassword" type="password" show-password />
        </el-form-item>

        <el-form-item label="确认密码" prop="confirmPassword">
          <el-input v-model="passwordForm.confirmPassword" type="password" show-password />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="showPasswordDialog = false">取消</el-button>
        <el-button @click="handleChangePassword" :loading="changingPassword" type="primary">
          确认修改
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { profileAPI } from '@/utils/api'
import { ElMessage, ElMessageBox } from 'element-plus'
import dayjs from 'dayjs'

const authStore = useAuthStore()

// 响应式数据
const saving = ref(false)
const savingPreferences = ref(false)
const changingPassword = ref(false)
const showPasswordDialog = ref(false)
const formRef = ref()
const passwordFormRef = ref()
const avatarInput = ref()

// 用户信息
const userInfo = reactive({
  username: '',
  email: '',
  phone: '',
  avatar: '',
  bio: '',
  birthday: '',
  gender: '',
  location: [],
  created_at: ''
})

// 统计数据
const stats = reactive({
  totalVideos: 0,
  totalDuration: 0,
  totalDownloads: 0
})

// 编辑表单
const editForm = reactive({
  username: '',
  email: '',
  phone: '',
  bio: '',
  birthday: '',
  gender: '',
  location: []
})

// 偏好设置
const preferences = reactive({
  videoRecommendation: true,
  downloadNotification: true,
  analyticsReport: false,
  publicHistory: false,
  dataCollection: true
})

// 密码表单
const passwordForm = reactive({
  currentPassword: '',
  newPassword: '',
  confirmPassword: ''
})

// 内容标签
const contentTags = ref([
  { id: 1, name: '搞笑', selected: false },
  { id: 2, name: '美食', selected: false },
  { id: 3, name: '旅行', selected: false },
  { id: 4, name: '音乐', selected: false },
  { id: 5, name: '舞蹈', selected: false },
  { id: 6, name: '宠物', selected: false },
  { id: 7, name: '游戏', selected: false },
  { id: 8, name: '知识', selected: false },
  { id: 9, name: '穿搭', selected: false },
  { id: 10, name: '健身', selected: false },
  { id: 11, name: '生活', selected: false },
  { id: 12, name: '情感', selected: false },
  { id: 13, name: '科技', selected: false }
])

// 地区选项
const locationOptions = ref([
  {
    value: 'beijing',
    label: '北京',
    children: [
      { value: 'haidian', label: '海淀区' },
      { value: 'chaoyang', label: '朝阳区' },
      { value: 'dongcheng', label: '东城区' }
    ]
  },
  {
    value: 'shanghai',
    label: '上海',
    children: [
      { value: 'huangpu', label: '黄浦区' },
      { value: 'xuhui', label: '徐汇区' },
      { value: 'changning', label: '长宁区' }
    ]
  }
])

// 表单验证规则
const formRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 2, max: 20, message: '用户名长度在 2 到 20 个字符', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱地址', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ],
  phone: [
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
  ]
}

const passwordRules = {
  currentPassword: [
    { required: true, message: '请输入当前密码', trigger: 'blur' }
  ],
  newPassword: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度在 6 到 20 个字符', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请确认新密码', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value !== passwordForm.newPassword) {
          callback(new Error('两次输入密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}

// 方法
const formatTime = (time) => {
  return dayjs(time).format('YYYY-MM-DD')
}

const formatDuration = (seconds) => {
  if (!seconds) return '0分钟'
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)

  if (hours > 0) {
    return `${hours}小时${minutes}分钟`
  }
  return `${minutes}分钟`
}

// 头像上传
const handleAvatarUpload = () => {
  avatarInput.value.click()
}

const handleAvatarChange = async (event) => {
  const file = event.target.files[0]
  if (!file) return

  // 检查文件类型
  if (!file.type.startsWith('image/')) {
    ElMessage.error('请选择图片文件')
    return
  }

  // 检查文件大小
  if (file.size > 2 * 1024 * 1024) {
    ElMessage.error('图片大小不能超过2MB')
    return
  }

  try {
    const formData = new FormData()
    formData.append('avatar', file)

    const response = await profileAPI.uploadAvatar(formData)
    if (response.success) {
      userInfo.avatar = response.data.avatar_url
      ElMessage.success('头像上传成功')
    }
  } catch (error) {
    ElMessage.error('头像上传失败')
  }
}

// 切换标签
const toggleTag = (tag) => {
  tag.selected = !tag.selected
}

// 保存资料
const handleSave = async () => {
  try {
    await formRef.value.validate()
    saving.value = true

    const response = await profileAPI.updateProfile(editForm)
    if (response.success) {
      Object.assign(userInfo, editForm)
      ElMessage.success('资料保存成功')
    }
  } catch (error) {
    if (error !== false) {
      ElMessage.error('保存失败')
    }
  } finally {
    saving.value = false
  }
}

// 重置表单
const handleReset = () => {
  Object.assign(editForm, userInfo)
}

// 保存偏好设置
const handleSavePreferences = async () => {
  try {
    savingPreferences.value = true

    const selectedTags = contentTags.value.filter(tag => tag.selected).map(tag => tag.id)
    const preferencesData = {
      ...preferences,
      contentTags: selectedTags
    }

    const response = await profileAPI.updatePreferences(preferencesData)
    if (response.success) {
      ElMessage.success('偏好设置保存成功')
    }
  } catch (error) {
    ElMessage.error('保存失败')
  } finally {
    savingPreferences.value = false
  }
}

// 修改密码
const handleChangePassword = async () => {
  try {
    await passwordFormRef.value.validate()
    changingPassword.value = true

    const response = await profileAPI.changePassword(passwordForm)
    if (response.success) {
      ElMessage.success('密码修改成功')
      showPasswordDialog.value = false

      // 重置表单
      Object.assign(passwordForm, {
        currentPassword: '',
        newPassword: '',
        confirmPassword: ''
      })
    }
  } catch (error) {
    if (error !== false) {
      ElMessage.error('密码修改失败')
    }
  } finally {
    changingPassword.value = false
  }
}

// 查看设备
const handleViewDevices = async () => {
  try {
    const response = await profileAPI.getLoginDevices()
    if (response.success) {
      // 这里可以打开一个设备管理弹窗
      ElMessage.info('设备管理功能开发中')
    }
  } catch (error) {
    ElMessage.error('获取设备信息失败')
  }
}

// 注销账号
const handleDeleteAccount = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要注销账号吗？此操作不可恢复，将删除所有相关数据！',
      '注销账号',
      {
        confirmButtonText: '确定注销',
        cancelButtonText: '取消',
        type: 'error'
      }
    )

    const response = await profileAPI.deleteAccount()
    if (response.success) {
      ElMessage.success('账号注销成功')
      await authStore.logout()
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('注销失败')
    }
  }
}

// 加载用户数据
const loadUserData = async () => {
  try {
    // 加载用户信息
    const profileResponse = await profileAPI.getProfile()
    if (profileResponse.success) {
      Object.assign(userInfo, profileResponse.data)
      Object.assign(editForm, profileResponse.data)
    }

    // 加载统计数据
    const statsResponse = await profileAPI.getUserStats()
    if (statsResponse.success) {
      Object.assign(stats, statsResponse.data)
    }

    // 加载偏好设置
    const preferencesResponse = await profileAPI.getPreferences()
    if (preferencesResponse.success) {
      Object.assign(preferences, preferencesResponse.data)

      // 设置内容标签选中状态
      if (preferencesResponse.data.contentTags) {
        contentTags.value.forEach(tag => {
          tag.selected = preferencesResponse.data.contentTags.includes(tag.id)
        })
      }
    }
  } catch (error) {
    console.error('加载用户数据失败:', error)
  }
}

// 组件挂载时加载数据
onMounted(() => {
  loadUserData()
})
</script>

<style lang="scss" scoped>
.profile-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 16px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  
  .header-content {
    h1 {
      font-size: 28px;
      font-weight: 600;
      color: var(--text-primary);
      margin: 0 0 8px 0;
    }
    
    p {
      font-size: 16px;
      color: var(--text-secondary);
      margin: 0;
    }
  }
}

.profile-card {
  margin-bottom: 24px;
  
  .user-card {
    border: none;
    border-radius: var(--border-radius-large);
    box-shadow: var(--shadow-light);
    overflow: hidden;
    
    :deep(.el-card__body) {
      padding: 24px;
    }
    
    .user-info {
      display: flex;
      flex-wrap: wrap;
      gap: 24px;
      
      .avatar-section {
        flex-shrink: 0;
        
        .avatar-container {
          position: relative;
          width: 120px;
          height: 120px;
          border-radius: 50%;
          overflow: hidden;
          border: 3px solid var(--primary-color);
          
          .user-avatar {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
          
          .avatar-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.3);
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            transition: opacity 0.2s;
            
            .el-button {
              --el-button-size: 40px;
              --el-button-text-color: white;
              --el-button-hover-text-color: white;
              --el-button-bg-color: var(--primary-color);
              --el-button-border-color: var(--primary-color);
              --el-button-hover-bg-color: var(--primary-light);
              --el-button-hover-border-color: var(--primary-light);
            }
          }
          
          &:hover {
            .avatar-overlay {
              opacity: 1;
            }
          }
        }
      }
      
      .user-details {
        flex: 1;
        min-width: 200px;
        
        h2 {
          font-size: 24px;
          font-weight: 600;
          color: var(--text-primary);
          margin: 0 0 8px 0;
        }
        
        .user-email {
          font-size: 16px;
          color: var(--text-secondary);
          margin: 0 0 16px 0;
        }
        
        .user-join-date {
          font-size: 14px;
          color: var(--text-placeholder);
          margin: 0 0 16px 0;
        }
        
        .user-stats {
          display: flex;
          flex-wrap: wrap;
          gap: 24px;
          
          .stat-item {
            .stat-value {
              font-size: 20px;
              font-weight: 600;
              color: var(--text-primary);
            }
            
            .stat-label {
              font-size: 12px;
              color: var(--text-secondary);
            }
          }
        }
      }
    }
  }
}

.edit-section {
  margin-bottom: 24px;
  
  .edit-card {
    border: none;
    border-radius: var(--border-radius-large);
    box-shadow: var(--shadow-light);
    overflow: hidden;
    
    :deep(.el-card__header) {
      padding: 16px 24px;
      border-bottom: 1px solid var(--border-lighter);
      font-size: 16px;
      font-weight: 500;
      color: var(--text-primary);
    }
    
    :deep(.el-card__body) {
      padding: 24px;
    }
    
    .edit-form {
      :deep(.el-form-item__label) {
        color: var(--text-primary);
        font-weight: 500;
      }
      
      :deep(.el-input__wrapper) {
        border-radius: var(--border-radius-base);
        
        &.is-focus {
          box-shadow: 0 0 0 1px var(--primary-color) inset;
        }
      }
      
      :deep(.el-textarea__inner) {
        border-radius: var(--border-radius-base);
        
        &:focus {
          box-shadow: 0 0 0 1px var(--primary-color) inset;
        }
      }
      
      :deep(.el-radio__input.is-checked .el-radio__inner) {
        background-color: var(--primary-color);
        border-color: var(--primary-color);
      }
      
      :deep(.el-radio__input.is-checked + .el-radio__label) {
        color: var(--primary-color);
      }
      
      :deep(.el-button--primary) {
        background-color: var(--primary-color);
        border-color: var(--primary-color);
        
        &:hover, &:focus {
          background-color: var(--primary-light);
          border-color: var(--primary-light);
        }
      }
    }
  }
}

.preferences-section {
  margin-bottom: 24px;
  
  .preferences-card {
    border: none;
    border-radius: var(--border-radius-large);
    box-shadow: var(--shadow-light);
    overflow: hidden;
    
    :deep(.el-card__header) {
      padding: 16px 24px;
      border-bottom: 1px solid var(--border-lighter);
      font-size: 16px;
      font-weight: 500;
      color: var(--text-primary);
    }
    
    :deep(.el-card__body) {
      padding: 24px;
    }
    
    .preferences-content {
      .preference-group {
        margin-bottom: 32px;
        
        &:last-child {
          margin-bottom: 0;
        }
        
        h4 {
          font-size: 16px;
          font-weight: 500;
          color: var(--text-primary);
          margin: 0 0 16px 0;
          padding-bottom: 8px;
          border-bottom: 1px solid var(--border-lighter);
        }
        
        .preference-tags {
          display: flex;
          flex-wrap: wrap;
          gap: 8px;
          margin-bottom: 16px;
          
          .preference-tag {
            margin: 0;
            cursor: pointer;
            transition: transform 0.2s;
            
            &:hover {
              transform: scale(1.05);
            }
            
            &.el-tag--primary {
              background-color: var(--primary-color);
              border-color: var(--primary-color);
            }
          }
        }
        
        .notification-settings, .privacy-settings {
          .setting-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid var(--border-lighter);
            
            &:last-child {
              border-bottom: none;
            }
            
            .setting-info {
              .setting-label {
                font-size: 14px;
                font-weight: 500;
                color: var(--text-primary);
                display: block;
                margin-bottom: 4px;
              }
              
              .setting-desc {
                font-size: 12px;
                color: var(--text-secondary);
              }
            }
            
            :deep(.el-switch.is-checked .el-switch__core) {
              background-color: var(--primary-color);
              border-color: var(--primary-color);
            }
          }
        }
      }
      
      .preferences-actions {
        margin-top: 24px;
        display: flex;
        justify-content: flex-end;
        
        .el-button {
          background-color: var(--primary-color);
          border-color: var(--primary-color);
          
          &:hover, &:focus {
            background-color: var(--primary-light);
            border-color: var(--primary-light);
          }
        }
      }
    }
  }
}

.security-section {
  .security-card {
    border: none;
    border-radius: var(--border-radius-large);
    box-shadow: var(--shadow-light);
    overflow: hidden;
    
    :deep(.el-card__header) {
      padding: 16px 24px;
      border-bottom: 1px solid var(--border-lighter);
      font-size: 16px;
      font-weight: 500;
      color: var(--text-primary);
    }
    
    :deep(.el-card__body) {
      padding: 24px;
    }
    
    .security-content {
      .security-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 16px 0;
        border-bottom: 1px solid var(--border-lighter);
        
        &:last-child {
          border-bottom: none;
        }
        
        .security-info {
          h4 {
            font-size: 16px;
            font-weight: 500;
            color: var(--text-primary);
            margin: 0 0 8px 0;
          }
          
          p {
            font-size: 14px;
            color: var(--text-secondary);
            margin: 0;
          }
        }
        
        .el-button {
          &.el-button--primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
            
            &:hover, &:focus {
              background-color: var(--primary-light);
              border-color: var(--primary-light);
            }
          }
        }
      }
    }
  }
}

:deep(.el-dialog) {
  border-radius: var(--border-radius-large);
  overflow: hidden;
  
  .el-dialog__header {
    padding: 16px 24px;
    border-bottom: 1px solid var(--border-lighter);
    margin-right: 0;
    
    .el-dialog__title {
      font-size: 16px;
      font-weight: 500;
      color: var(--text-primary);
    }
  }
  
  .el-dialog__body {
    padding: 24px;
  }
  
  .el-dialog__footer {
    padding: 16px 24px;
    border-top: 1px solid var(--border-lighter);
  }
  
  .el-form-item__label {
    color: var(--text-primary);
    font-weight: 500;
  }
  
  .el-input__wrapper {
    border-radius: var(--border-radius-base);
    
    &.is-focus {
      box-shadow: 0 0 0 1px var(--primary-color) inset;
    }
  }
  
  .el-button--primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    
    &:hover, &:focus {
      background-color: var(--primary-light);
      border-color: var(--primary-light);
    }
  }
}

// 响应式
@media (max-width: 768px) {
  .user-info {
    flex-direction: column;
    align-items: center;
    text-align: center;
    
    .user-details {
      text-align: center;
      
      .user-stats {
        justify-content: center;
      }
    }
  }
  
  .security-item {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }
}

// 暗色主题适配
.dark {
  .avatar-container {
    background-color: #2a2a2a;
  }
}
</style>
