import httpx
import json
import time
import uuid
import qrcode
import io
import base64
from typing import Dict, Optional, Any
from ..core.config import settings, douyin_config


class DouyinOAuthService:
    """抖音OAuth服务类"""
    
    def __init__(self):
        self.client_key = settings.DOUYIN_CLIENT_KEY
        self.client_secret = settings.DOUYIN_CLIENT_SECRET
        self.redirect_uri = settings.DOUYIN_REDIRECT_URI
        self.oauth_base_url = douyin_config.DOUYIN_OAUTH_URL
        self.api_base_url = douyin_config.DOUYIN_OPEN_API_URL
        self.qr_tokens = {}  # 存储二维码token和状态
        
        # 创建HTTP客户端
        self.client = httpx.AsyncClient(
            timeout=30.0,
            follow_redirects=True
        )
    
    async def __aenter__(self):
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.client.aclose()
    
    def _generate_state(self) -> str:
        """生成随机状态码"""
        return str(uuid.uuid4())
    
    def _generate_qr_token(self) -> str:
        """生成二维码token"""
        token = str(uuid.uuid4())
        self.qr_tokens[token] = {
            "status": "waiting",
            "created_at": time.time(),
            "user_info": None
        }
        return token
    
    def _clean_expired_tokens(self, expiry_seconds: int = 300):
        """清理过期的token"""
        now = time.time()
        expired_tokens = []
        
        for token, data in self.qr_tokens.items():
            if now - data["created_at"] > expiry_seconds:
                expired_tokens.append(token)
                
        for token in expired_tokens:
            if token in self.qr_tokens:
                self.qr_tokens.pop(token)
    
    def _get_auth_url(self, state: str) -> str:
        """获取抖音授权URL"""
        scope = "user_info,video.list,data.external.item"
        params = {
            "client_key": self.client_key,
            "response_type": "code",
            "scope": scope,
            "redirect_uri": self.redirect_uri,
            "state": state
        }
        
        query_string = "&".join([f"{k}={v}" for k, v in params.items()])
        return f"{self.oauth_base_url}/oauth/connect/?{query_string}"
    
    async def generate_qrcode(self) -> Dict[str, Any]:
        """生成抖音登录二维码"""
        # 清理过期token
        self._clean_expired_tokens()
        
        # 生成token和state
        qr_token = self._generate_qr_token()
        state = self._generate_state()
        
        # 存储state和token的关联
        self.qr_tokens[qr_token]["state"] = state
        
        # 获取授权URL
        auth_url = self._get_auth_url(state)
        
        # 生成二维码
        qr = qrcode.QRCode(
            version=1,
            error_correction=qrcode.constants.ERROR_CORRECT_L,
            box_size=10,
            border=4,
        )
        qr.add_data(auth_url)
        qr.make(fit=True)
        
        img = qr.make_image(fill_color="black", back_color="white")
        
        # 转换为base64
        buffered = io.BytesIO()
        img.save(buffered)
        img_str = base64.b64encode(buffered.getvalue()).decode()
        
        return {
            "qr_code": f"data:image/png;base64,{img_str}",
            "token": qr_token,
            "expires_in": 300  # 5分钟过期
        }
    
    def check_qrcode_status(self, token: str) -> Dict[str, Any]:
        """检查二维码状态"""
        if token not in self.qr_tokens:
            return {"status": "expired"}
        
        # 检查是否过期
        now = time.time()
        if now - self.qr_tokens[token]["created_at"] > 300:  # 5分钟过期
            self.qr_tokens.pop(token)
            return {"status": "expired"}
        
        status = self.qr_tokens[token]["status"]
        result = {"status": status}
        
        # 如果已确认，返回用户信息
        if status == "confirmed" and self.qr_tokens[token]["user_info"]:
            result["user_info"] = self.qr_tokens[token]["user_info"]
            # 使用后清理token
            self.qr_tokens.pop(token)
        
        return result
    
    def update_qrcode_status(self, token: str, status: str, user_info: Optional[Dict] = None) -> bool:
        """更新二维码状态"""
        if token not in self.qr_tokens:
            return False
        
        self.qr_tokens[token]["status"] = status
        if user_info:
            self.qr_tokens[token]["user_info"] = user_info
        
        return True
    
    async def get_access_token(self, code: str) -> Dict[str, Any]:
        """通过授权码获取访问令牌"""
        url = f"{self.api_base_url}/oauth/access_token/"
        params = {
            "client_key": self.client_key,
            "client_secret": self.client_secret,
            "code": code,
            "grant_type": "authorization_code"
        }
        
        try:
            response = await self.client.post(url, data=params)
            response.raise_for_status()
            data = response.json()
            
            if "data" in data and "access_token" in data["data"]:
                return data["data"]
            else:
                raise Exception(f"获取访问令牌失败: {data.get('message', '未知错误')}")
                
        except httpx.HTTPError as e:
            raise Exception(f"请求失败: {str(e)}")
        except json.JSONDecodeError:
            raise Exception("响应格式错误")
    
    async def get_user_info(self, access_token: str, open_id: str) -> Dict[str, Any]:
        """获取用户信息"""
        url = f"{self.api_base_url}/oauth/userinfo/"
        params = {
            "access_token": access_token,
            "open_id": open_id
        }
        
        try:
            response = await self.client.get(url, params=params)
            response.raise_for_status()
            data = response.json()
            
            if "data" in data:
                return data["data"]
            else:
                raise Exception(f"获取用户信息失败: {data.get('message', '未知错误')}")
                
        except httpx.HTTPError as e:
            raise Exception(f"请求失败: {str(e)}")
        except json.JSONDecodeError:
            raise Exception("响应格式错误")
    
    async def refresh_token(self, refresh_token: str) -> Dict[str, Any]:
        """刷新访问令牌"""
        url = f"{self.api_base_url}/oauth/refresh_token/"
        params = {
            "client_key": self.client_key,
            "grant_type": "refresh_token",
            "refresh_token": refresh_token
        }
        
        try:
            response = await self.client.post(url, data=params)
            response.raise_for_status()
            data = response.json()
            
            if "data" in data and "access_token" in data["data"]:
                return data["data"]
            else:
                raise Exception(f"刷新令牌失败: {data.get('message', '未知错误')}")
                
        except httpx.HTTPError as e:
            raise Exception(f"请求失败: {str(e)}")
        except json.JSONDecodeError:
            raise Exception("响应格式错误")
    
    async def renew_refresh_token(self, refresh_token: str) -> Dict[str, Any]:
        """更新刷新令牌"""
        url = f"{self.api_base_url}/oauth/renew_refresh_token/"
        params = {
            "client_key": self.client_key,
            "refresh_token": refresh_token
        }
        
        try:
            response = await self.client.post(url, data=params)
            response.raise_for_status()
            data = response.json()
            
            if "data" in data and "refresh_token" in data["data"]:
                return data["data"]
            else:
                raise Exception(f"更新刷新令牌失败: {data.get('message', '未知错误')}")
                
        except httpx.HTTPError as e:
            raise Exception(f"请求失败: {str(e)}")
        except json.JSONDecodeError:
            raise Exception("响应格式错误")
    
    async def handle_oauth_callback(self, code: str, state: str) -> Dict[str, Any]:
        """处理OAuth回调"""
        # 查找对应的qr_token
        qr_token = None
        for token, data in self.qr_tokens.items():
            if data.get("state") == state:
                qr_token = token
                break
        
        if not qr_token:
            raise Exception("无效的状态码")
        
        # 更新状态为已扫描
        self.update_qrcode_status(qr_token, "scanned")
        
        # 获取访问令牌
        token_data = await self.get_access_token(code)
        
        # 获取用户信息
        user_info = await self.get_user_info(token_data["access_token"], token_data["open_id"])
        
        # 合并用户信息和令牌信息
        result = {
            **token_data,
            "user_info": user_info
        }
        
        # 更新状态为已确认
        self.update_qrcode_status(qr_token, "confirmed", result)
        
        return result


# 创建服务实例
douyin_oauth_service = DouyinOAuthService() 