import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { authAPI } from '@/utils/api'
import Cookies from 'js-cookie'

export const useAuthStore = defineStore('auth', () => {
  // 状态
  const token = ref(Cookies.get('access_token') || '')
  const refreshToken = ref(Cookies.get('refresh_token') || '')
  const user = ref(null)
  const loading = ref(false)
  const douyinUser = ref(null)

  // 计算属性
  const isAuthenticated = computed(() => !!token.value && !!user.value)
  const userInfo = computed(() => user.value || {})
  const hasDouyinAuth = computed(() => !!douyinUser.value)

  // 设置token
  const setToken = (accessToken, refreshTokenValue) => {
    token.value = accessToken
    refreshToken.value = refreshTokenValue
    
    // 保存到Cookie
    Cookies.set('access_token', accessToken, { expires: 7 })
    Cookies.set('refresh_token', refreshTokenValue, { expires: 30 })
  }

  // 清除token
  const clearToken = () => {
    token.value = ''
    refreshToken.value = ''
    user.value = null
    douyinUser.value = null
    
    // 清除Cookie
    Cookies.remove('access_token')
    Cookies.remove('refresh_token')
  }

  // 登录
  const login = async (credentials) => {
    try {
      loading.value = true
      const response = await authAPI.login(credentials)
      
      if (response.success) {
        const { access_token, refresh_token } = response.data
        setToken(access_token, refresh_token)
        
        // 获取用户信息
        await getCurrentUser()
        
        ElMessage.success('登录成功')
        return true
      }
      return false
    } catch (error) {
      ElMessage.error(error.message || '登录失败')
      return false
    } finally {
      loading.value = false
    }
  }

  // 抖音二维码登录
  const douyinQRLogin = async (authData) => {
    try {
      loading.value = true
      
      // 设置token
      setToken(authData.access_token, authData.refresh_token)
      
      // 设置用户信息
      douyinUser.value = {
        ...authData.user_info,
        bind_time: new Date().toISOString()
      }
      
      // 创建或更新用户
      const response = await authAPI.douyinAuth({
        access_token: authData.access_token,
        refresh_token: authData.refresh_token,
        user_info: authData.user_info
      })
      
      if (response.success) {
        user.value = response.data
        return true
      }
      
      return false
    } catch (error) {
      ElMessage.error(error.message || '登录失败')
      clearToken()
      return false
    } finally {
      loading.value = false
    }
  }

  // 注册
  const register = async (userData) => {
    try {
      loading.value = true
      const response = await authAPI.register(userData)
      
      if (response.success) {
        ElMessage.success('注册成功，请登录')
        return true
      }
      return false
    } catch (error) {
      ElMessage.error(error.message || '注册失败')
      return false
    } finally {
      loading.value = false
    }
  }

  // 登出
  const logout = async () => {
    try {
      await authAPI.logout()
    } catch (error) {
      console.error('登出请求失败:', error)
    } finally {
      clearToken()
      ElMessage.success('已退出登录')
    }
  }

  // 获取当前用户信息
  const getCurrentUser = async () => {
    try {
      const response = await authAPI.getCurrentUser()
      if (response.success) {
        user.value = response.data
        
        // 如果有抖音用户信息，设置douyinUser
        if (response.data.douyin_info) {
          douyinUser.value = response.data.douyin_info
        }
        
        return response.data
      }
      return null
    } catch (error) {
      console.error('获取用户信息失败:', error)
      // 如果获取用户信息失败，可能是token过期
      if (error.response?.status === 401) {
        await tryRefreshToken()
      }
      return null
    }
  }

  // 更新用户信息
  const updateUser = async (userData) => {
    try {
      loading.value = true
      const response = await authAPI.updateUser(userData)
      
      if (response.success) {
        user.value = { ...user.value, ...response.data }
        ElMessage.success('用户信息更新成功')
        return true
      }
      return false
    } catch (error) {
      ElMessage.error(error.message || '更新失败')
      return false
    } finally {
      loading.value = false
    }
  }

  // 修改密码
  const changePassword = async (passwordData) => {
    try {
      loading.value = true
      const response = await authAPI.changePassword(passwordData)
      
      if (response.success) {
        ElMessage.success('密码修改成功')
        return true
      }
      return false
    } catch (error) {
      ElMessage.error(error.message || '密码修改失败')
      return false
    } finally {
      loading.value = false
    }
  }

  // 抖音账号认证
  const douyinAuth = async (authData) => {
    try {
      loading.value = true
      const response = await authAPI.douyinAuth(authData)
      
      if (response.success) {
        user.value = { ...user.value, ...response.data }
        
        if (response.data.douyin_info) {
          douyinUser.value = response.data.douyin_info
        }
        
        ElMessage.success('抖音账号绑定成功')
        return true
      }
      return false
    } catch (error) {
      ElMessage.error(error.message || '抖音账号绑定失败')
      return false
    } finally {
      loading.value = false
    }
  }

  // 刷新token
  const tryRefreshToken = async () => {
    if (!refreshToken.value) {
      clearToken()
      return false
    }

    try {
      const response = await authAPI.refreshToken(refreshToken.value)
      
      if (response.success) {
        const { access_token, refresh_token } = response.data
        setToken(access_token, refresh_token)
        return true
      }
      
      clearToken()
      return false
    } catch (error) {
      console.error('刷新token失败:', error)
      clearToken()
      return false
    }
  }

  // 初始化认证状态
  const initAuth = async () => {
    if (token.value) {
      const userInfo = await getCurrentUser()
      if (!userInfo) {
        // 如果获取用户信息失败，尝试刷新token
        const refreshed = await tryRefreshToken()
        if (refreshed) {
          await getCurrentUser()
        }
      }
    }
  }

  // 设置抖音认证信息
  const setDouyinAuth = async (douyinUserInfo) => {
    douyinUser.value = {
      ...douyinUserInfo,
      bind_time: new Date().toISOString()
    }
    return true
  }

  // 清除抖音认证信息
  const clearDouyinAuth = async () => {
    douyinUser.value = null
    return true
  }

  return {
    // 状态
    token,
    refreshToken,
    user,
    loading,
    douyinUser,
    
    // 计算属性
    isAuthenticated,
    userInfo,
    hasDouyinAuth,
    
    // 方法
    login,
    douyinQRLogin,
    register,
    logout,
    getCurrentUser,
    updateUser,
    changePassword,
    douyinAuth,
    tryRefreshToken,
    initAuth,
    setDouyinAuth,
    clearDouyinAuth
  }
})
