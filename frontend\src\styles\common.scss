// 通用样式类

// 布局相关
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 16px;
}

.flex {
  display: flex;
}

.flex-center {
  display: flex;
  justify-content: center;
  align-items: center;
}

.flex-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

.flex-wrap {
  flex-wrap: wrap;
}

.flex-1 {
  flex: 1;
}

// 文本相关
.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.text-ellipsis {
  @include text-ellipsis(1);
}

.text-ellipsis-2 {
  @include text-ellipsis(2);
}

.text-ellipsis-3 {
  @include text-ellipsis(3);
}

// 间距相关
.m-0 { margin: 0; }
.mt-0 { margin-top: 0; }
.mr-0 { margin-right: 0; }
.mb-0 { margin-bottom: 0; }
.ml-0 { margin-left: 0; }

.m-1 { margin: 8px; }
.mt-1 { margin-top: 8px; }
.mr-1 { margin-right: 8px; }
.mb-1 { margin-bottom: 8px; }
.ml-1 { margin-left: 8px; }

.m-2 { margin: 16px; }
.mt-2 { margin-top: 16px; }
.mr-2 { margin-right: 16px; }
.mb-2 { margin-bottom: 16px; }
.ml-2 { margin-left: 16px; }

.m-3 { margin: 24px; }
.mt-3 { margin-top: 24px; }
.mr-3 { margin-right: 24px; }
.mb-3 { margin-bottom: 24px; }
.ml-3 { margin-left: 24px; }

.p-0 { padding: 0; }
.pt-0 { padding-top: 0; }
.pr-0 { padding-right: 0; }
.pb-0 { padding-bottom: 0; }
.pl-0 { padding-left: 0; }

.p-1 { padding: 8px; }
.pt-1 { padding-top: 8px; }
.pr-1 { padding-right: 8px; }
.pb-1 { padding-bottom: 8px; }
.pl-1 { padding-left: 8px; }

.p-2 { padding: 16px; }
.pt-2 { padding-top: 16px; }
.pr-2 { padding-right: 16px; }
.pb-2 { padding-bottom: 16px; }
.pl-2 { padding-left: 16px; }

.p-3 { padding: 24px; }
.pt-3 { padding-top: 24px; }
.pr-3 { padding-right: 24px; }
.pb-3 { padding-bottom: 24px; }
.pl-3 { padding-left: 24px; }

// 显示/隐藏
.hidden {
  display: none;
}

.invisible {
  visibility: hidden;
}

.visible {
  visibility: visible;
}

// 位置相关
.relative {
  position: relative;
}

.absolute {
  position: absolute;
}

.fixed {
  position: fixed;
}

.sticky {
  position: sticky;
}

// 宽高相关
.w-full {
  width: 100%;
}

.h-full {
  height: 100%;
}

.w-auto {
  width: auto;
}

.h-auto {
  height: auto;
}

// 圆角
.rounded {
  border-radius: 4px;
}

.rounded-lg {
  border-radius: 8px;
}

.rounded-full {
  border-radius: 50%;
}

// 阴影
.shadow {
  @include card-shadow(1);
}

.shadow-lg {
  @include card-shadow(2);
}

.shadow-xl {
  @include card-shadow(3);
}

// 过渡动画
.transition {
  transition: all 0.3s ease;
}

.transition-fast {
  transition: all 0.15s ease;
}

.transition-slow {
  transition: all 0.5s ease;
}

// 鼠标样式
.cursor-pointer {
  cursor: pointer;
}

.cursor-not-allowed {
  cursor: not-allowed;
}

// 选择禁用
.select-none {
  user-select: none;
}

// 滚动条样式
.scrollbar-thin {
  &::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
  }
}
