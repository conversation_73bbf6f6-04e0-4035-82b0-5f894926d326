// SCSS Mixins - 可复用的样式混合器

// 清除浮动
@mixin clearfix {
  &::after {
    content: '';
    display: table;
    clear: both;
  }
}

// 文本省略号
@mixin text-ellipsis($lines: 1) {
  @if $lines == 1 {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  } @else {
    display: -webkit-box;
    -webkit-line-clamp: $lines;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

// 居中对齐
@mixin center($type: both) {
  position: absolute;
  @if $type == both {
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  } @else if $type == horizontal {
    left: 50%;
    transform: translateX(-50%);
  } @else if $type == vertical {
    top: 50%;
    transform: translateY(-50%);
  }
}

// Flex 布局
@mixin flex($direction: row, $justify: center, $align: center) {
  display: flex;
  flex-direction: $direction;
  justify-content: $justify;
  align-items: $align;
}

// 响应式断点
@mixin respond-to($breakpoint) {
  @if $breakpoint == mobile {
    @media (max-width: 767px) {
      @content;
    }
  } @else if $breakpoint == tablet {
    @media (min-width: 768px) and (max-width: 1023px) {
      @content;
    }
  } @else if $breakpoint == desktop {
    @media (min-width: 1024px) {
      @content;
    }
  }
}

// 按钮样式
@mixin button-style($bg-color, $text-color: #fff, $hover-bg: darken($bg-color, 10%)) {
  background-color: $bg-color;
  color: $text-color;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s ease;

  &:hover {
    background-color: $hover-bg;
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
}

// 卡片阴影
@mixin card-shadow($level: 1) {
  @if $level == 1 {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  } @else if $level == 2 {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  } @else if $level == 3 {
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
  }
}
