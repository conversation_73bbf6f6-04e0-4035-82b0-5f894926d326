from sqlalchemy import Column, Integer, String, DateTime, Boolean, Text
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from ..core.database import Base


class User(Base):
    """用户模型"""
    __tablename__ = "users"
    
    id = Column(Integer, primary_key=True, index=True)
    username = Column(String(50), unique=True, index=True, nullable=False)
    email = Column(String(100), unique=True, index=True, nullable=True)
    hashed_password = Column(String(255), nullable=False)
    full_name = Column(String(100), nullable=True)
    avatar_url = Column(String(500), nullable=True)
    
    # 抖音相关信息
    douyin_uid = Column(String(50), nullable=True, index=True)
    douyin_sec_uid = Column(String(100), nullable=True)
    douyin_nickname = Column(String(100), nullable=True)
    douyin_cookie = Column(Text, nullable=True)
    
    # 抖音OAuth相关信息
    douyin_open_id = Column(String(100), nullable=True, index=True)
    douyin_access_token = Column(String(255), nullable=True)
    douyin_refresh_token = Column(String(255), nullable=True)
    douyin_expires_in = Column(Integer, nullable=True)
    douyin_scope = Column(String(255), nullable=True)
    douyin_token_updated_at = Column(DateTime(timezone=True), nullable=True)
    
    # 状态字段
    is_active = Column(Boolean, default=True)
    is_verified = Column(Boolean, default=False)
    is_premium = Column(Boolean, default=False)
    
    # 时间字段
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    last_login = Column(DateTime(timezone=True), nullable=True)
    
    # 设置字段
    settings = Column(Text, nullable=True)  # JSON格式存储用户设置
    
    # 关系
    videos = relationship("Video", back_populates="user")
    history_records = relationship("HistoryRecord", back_populates="user")
    download_tasks = relationship("DownloadTask", back_populates="user")
    
    def __repr__(self):
        return f"<User(id={self.id}, username='{self.username}')>"


class UserSession(Base):
    """用户会话模型"""
    __tablename__ = "user_sessions"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, nullable=False, index=True)
    session_token = Column(String(255), unique=True, nullable=False)
    refresh_token = Column(String(255), unique=True, nullable=True)
    device_info = Column(Text, nullable=True)  # JSON格式存储设备信息
    ip_address = Column(String(45), nullable=True)
    user_agent = Column(Text, nullable=True)
    
    # 时间字段
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    expires_at = Column(DateTime(timezone=True), nullable=False)
    last_used = Column(DateTime(timezone=True), server_default=func.now())
    
    # 状态
    is_active = Column(Boolean, default=True)
    
    def __repr__(self):
        return f"<UserSession(id={self.id}, user_id={self.user_id})>"
