from pydantic_settings import BaseSettings
from typing import List, Optional
import os
from pathlib import Path


class Settings(BaseSettings):
    """应用配置类"""
    
    # 基础配置
    PROJECT_NAME: str = "抖音历史记录分析工具"
    VERSION: str = "1.0.0"
    DESCRIPTION: str = "基于FastAPI + Vue 3的抖音历史记录分析工具"
    
    # 服务器配置
    HOST: str = "0.0.0.0"
    PORT: int = 8000
    DEBUG: bool = True
    
    # 数据库配置
    DATABASE_URL: str = "sqlite:///./douyin_history.db"
    
    # 安全配置
    SECRET_KEY: str = "your-super-secret-key-change-this-in-production"
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    
    # DeepSeek AI配置
    DEEPSEEK_API_KEY: Optional[str] = None
    DEEPSEEK_BASE_URL: str = "https://api.deepseek.com"
    DEEPSEEK_MODEL: str = "deepseek-chat"
    
    # 抖音配置
    DOUYIN_COOKIE: Optional[str] = None
    DOUYIN_USER_AGENT: str = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
    DOUYIN_BASE_URL: str = "https://www.douyin.com"
    
    # 抖音OAuth配置
    DOUYIN_CLIENT_KEY: str = "your-douyin-client-key"
    DOUYIN_CLIENT_SECRET: str = "your-douyin-client-secret"
    DOUYIN_REDIRECT_URI: str = "http://localhost:8000/api/v1/auth/douyin-callback"
    
    # TikTok配置
    TIKTOK_COOKIE: Optional[str] = None
    TIKTOK_USER_AGENT: str = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
    
    # 下载配置
    DOWNLOAD_PATH: str = "./downloads"
    MAX_CONCURRENT_DOWNLOADS: int = 5
    DOWNLOAD_TIMEOUT: int = 300
    CHUNK_SIZE: int = 8192
    
    # FFmpeg配置
    FFMPEG_PATH: str = "ffmpeg"
    
    # 日志配置
    LOG_LEVEL: str = "INFO"
    LOG_FILE: str = "./logs/app.log"
    
    # CORS配置
    ALLOWED_ORIGINS: List[str] = ["http://localhost:3000", "http://127.0.0.1:3000"]
    ALLOWED_METHODS: List[str] = ["GET", "POST", "PUT", "DELETE", "OPTIONS"]
    ALLOWED_HEADERS: List[str] = ["*"]
    
    # 任务调度配置
    SCHEDULER_TIMEZONE: str = "Asia/Shanghai"
    AUTO_SYNC_INTERVAL: int = 3600  # 秒
    
    # 缓存配置
    CACHE_TTL: int = 3600  # 秒
    MAX_CACHE_SIZE: int = 1000
    
    # API限制
    RATE_LIMIT_PER_MINUTE: int = 60
    MAX_HISTORY_ITEMS: int = 1000
    MAX_DOWNLOAD_ITEMS: int = 100
    
    # 文件上传配置
    MAX_FILE_SIZE: int = 100 * 1024 * 1024  # 100MB
    ALLOWED_FILE_TYPES: List[str] = [".mp4", ".jpg", ".jpeg", ".png", ".gif"]
    
    class Config:
        env_file = ".env"
        case_sensitive = True
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        # 确保下载目录存在
        Path(self.DOWNLOAD_PATH).mkdir(parents=True, exist_ok=True)
        # 确保日志目录存在
        Path(self.LOG_FILE).parent.mkdir(parents=True, exist_ok=True)


# 创建全局配置实例
settings = Settings()


# 抖音API相关配置
class DouyinConfig:
    """抖音API配置"""
    
    # API端点
    ENDPOINTS = {
        "video_info": "/aweme/v1/web/aweme/detail/",
        "user_info": "/aweme/v1/web/im/user/info/",
        "user_posts": "/aweme/v1/web/aweme/post/",
        "user_likes": "/aweme/v1/web/aweme/favorite/",
        "user_collections": "/aweme/v1/web/aweme/listcollection/",
        "history": "/aweme/v1/web/history/read/",
        "comments": "/aweme/v1/web/comment/list/",
        "live_info": "/webcast/room/web/enter/",
        "search": "/aweme/v1/web/search/item/",
    }
    
    # 请求头
    HEADERS = {
        "Accept": "application/json, text/plain, */*",
        "Accept-Encoding": "gzip, deflate, br",
        "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
        "Cache-Control": "no-cache",
        "Pragma": "no-cache",
        "Referer": "https://www.douyin.com/",
        "Sec-Ch-Ua": '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
        "Sec-Ch-Ua-Mobile": "?0",
        "Sec-Ch-Ua-Platform": '"Windows"',
        "Sec-Fetch-Dest": "empty",
        "Sec-Fetch-Mode": "cors",
        "Sec-Fetch-Site": "same-origin",
        "User-Agent": settings.DOUYIN_USER_AGENT,
    }
    
    # 参数配置
    DEFAULT_PARAMS = {
        "device_platform": "webapp",
        "aid": "6383",
        "channel": "channel_pc_web",
        "version_code": "170400",
        "version_name": "17.4.0",
        "cookie_enabled": "true",
        "screen_width": "1920",
        "screen_height": "1080",
        "browser_language": "zh-CN",
        "browser_platform": "Win32",
        "browser_name": "Chrome",
        "browser_version": "120.0.0.0",
    }
    
    # OAuth相关URL
    DOUYIN_OAUTH_URL = "https://open.douyin.com/platform"
    DOUYIN_OPEN_API_URL = "https://open.douyin.com/api"


# TikTok API相关配置
class TikTokConfig:
    """TikTok API配置"""
    
    # API端点
    ENDPOINTS = {
        "video_info": "/api/item/detail/",
        "user_info": "/api/user/detail/",
        "user_posts": "/api/post/item_list/",
        "comments": "/api/comment/list/",
    }
    
    # 请求头
    HEADERS = {
        "Accept": "*/*",
        "Accept-Encoding": "gzip, deflate, br",
        "Accept-Language": "en-US,en;q=0.9",
        "Cache-Control": "no-cache",
        "Pragma": "no-cache",
        "Referer": "https://www.tiktok.com/",
        "Sec-Ch-Ua": '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
        "Sec-Ch-Ua-Mobile": "?0",
        "Sec-Ch-Ua-Platform": '"Windows"',
        "Sec-Fetch-Dest": "empty",
        "Sec-Fetch-Mode": "cors",
        "Sec-Fetch-Site": "same-origin",
        "User-Agent": settings.TIKTOK_USER_AGENT,
    }


# 导出配置
douyin_config = DouyinConfig()
tiktok_config = TikTokConfig()
