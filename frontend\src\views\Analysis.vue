<template>
  <div class="analysis-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h1>数据分析</h1>
        <p>深入了解您的观看习惯和偏好</p>
      </div>
      <div class="header-actions">
        <el-button @click="handleRefresh" :loading="loading" type="primary">
          <el-icon><Refresh /></el-icon>
          刷新数据
        </el-button>
        <el-button @click="handleExport" type="success">
          <el-icon><Download /></el-icon>
          导出报告
        </el-button>
      </div>
    </div>

    <!-- 时间范围选择 -->
    <div class="time-range-section">
      <el-card class="range-card">
        <div class="range-content">
          <div class="range-selector">
            <el-radio-group v-model="timeRange" @change="handleTimeRangeChange">
              <el-radio-button label="7d">最近7天</el-radio-button>
              <el-radio-button label="30d">最近30天</el-radio-button>
              <el-radio-button label="90d">最近90天</el-radio-button>
              <el-radio-button label="1y">最近一年</el-radio-button>
              <el-radio-button label="all">全部</el-radio-button>
            </el-radio-group>
          </div>
          <div class="custom-range">
            <el-date-picker
              v-model="customDateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              @change="handleCustomRangeChange"
              size="small"
            />
          </div>
        </div>
      </el-card>
    </div>

    <!-- 概览统计 -->
    <div class="overview-section">
      <el-row :gutter="16">
        <el-col :xs="12" :sm="6" :md="6" :lg="6">
          <div class="overview-card">
            <div class="card-icon videos">
              <el-icon><VideoPlay /></el-icon>
            </div>
            <div class="card-content">
              <div class="card-value">{{ stats.totalVideos }}</div>
              <div class="card-label">观看视频</div>
              <div class="card-change" :class="{ positive: stats.videosChange > 0, negative: stats.videosChange < 0 }">
                <el-icon v-if="stats.videosChange > 0"><ArrowUp /></el-icon>
                <el-icon v-else-if="stats.videosChange < 0"><ArrowDown /></el-icon>
                {{ Math.abs(stats.videosChange) }}%
              </div>
            </div>
          </div>
        </el-col>

        <el-col :xs="12" :sm="6" :md="6" :lg="6">
          <div class="overview-card">
            <div class="card-icon duration">
              <el-icon><Timer /></el-icon>
            </div>
            <div class="card-content">
              <div class="card-value">{{ formatDuration(stats.totalDuration) }}</div>
              <div class="card-label">观看时长</div>
              <div class="card-change" :class="{ positive: stats.durationChange > 0, negative: stats.durationChange < 0 }">
                <el-icon v-if="stats.durationChange > 0"><ArrowUp /></el-icon>
                <el-icon v-else-if="stats.durationChange < 0"><ArrowDown /></el-icon>
                {{ Math.abs(stats.durationChange) }}%
              </div>
            </div>
          </div>
        </el-col>

        <el-col :xs="12" :sm="6" :md="6" :lg="6">
          <div class="overview-card">
            <div class="card-icon engagement">
              <el-icon><Star /></el-icon>
            </div>
            <div class="card-content">
              <div class="card-value">{{ stats.avgEngagement }}%</div>
              <div class="card-label">平均互动率</div>
              <div class="card-change" :class="{ positive: stats.engagementChange > 0, negative: stats.engagementChange < 0 }">
                <el-icon v-if="stats.engagementChange > 0"><ArrowUp /></el-icon>
                <el-icon v-else-if="stats.engagementChange < 0"><ArrowDown /></el-icon>
                {{ Math.abs(stats.engagementChange) }}%
              </div>
            </div>
          </div>
        </el-col>

        <el-col :xs="12" :sm="6" :md="6" :lg="6">
          <div class="overview-card">
            <div class="card-icon authors">
              <el-icon><User /></el-icon>
            </div>
            <div class="card-content">
              <div class="card-value">{{ stats.uniqueAuthors }}</div>
              <div class="card-label">关注作者</div>
              <div class="card-change" :class="{ positive: stats.authorsChange > 0, negative: stats.authorsChange < 0 }">
                <el-icon v-if="stats.authorsChange > 0"><ArrowUp /></el-icon>
                <el-icon v-else-if="stats.authorsChange < 0"><ArrowDown /></el-icon>
                {{ Math.abs(stats.authorsChange) }}%
              </div>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 图表区域 -->
    <div class="charts-section">
      <el-row :gutter="16">
        <!-- 观看趋势图 -->
        <el-col :xs="24" :sm="24" :md="12" :lg="12">
          <el-card class="chart-card">
            <template #header>
              <div class="chart-header">
                <span>观看趋势</span>
                <el-select v-model="trendMetric" size="small" style="width: 120px">
                  <el-option label="视频数量" value="count" />
                  <el-option label="观看时长" value="duration" />
                </el-select>
              </div>
            </template>
            <div class="chart-container">
              <v-chart
                :option="trendChartOption"
                :loading="loading"
                style="height: 300px"
              />
            </div>
          </el-card>
        </el-col>

        <!-- 时间分布图 -->
        <el-col :xs="24" :sm="24" :md="12" :lg="12">
          <el-card class="chart-card">
            <template #header>
              <span>观看时间分布</span>
            </template>
            <div class="chart-container">
              <v-chart
                :option="timeDistributionOption"
                :loading="loading"
                style="height: 300px"
              />
            </div>
          </el-card>
        </el-col>

        <!-- 内容类型分布 -->
        <el-col :xs="24" :sm="24" :md="12" :lg="12">
          <el-card class="chart-card">
            <template #header>
              <span>内容类型分布</span>
            </template>
            <div class="chart-container">
              <v-chart
                :option="categoryDistributionOption"
                :loading="loading"
                style="height: 300px"
              />
            </div>
          </el-card>
        </el-col>

        <!-- 作者排行 -->
        <el-col :xs="24" :sm="24" :md="12" :lg="12">
          <el-card class="chart-card">
            <template #header>
              <span>热门作者</span>
            </template>
            <div class="chart-container">
              <v-chart
                :option="authorRankingOption"
                :loading="loading"
                style="height: 300px"
              />
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 详细分析 -->
    <div class="detailed-analysis">
      <el-row :gutter="16">
        <!-- 观看习惯分析 -->
        <el-col :xs="24" :sm="24" :md="12" :lg="8">
          <el-card class="analysis-card">
            <template #header>
              <span>观看习惯分析</span>
            </template>
            <div class="habit-analysis">
              <div class="habit-item">
                <div class="habit-label">最活跃时段</div>
                <div class="habit-value">{{ habits.mostActiveTime }}</div>
              </div>
              <div class="habit-item">
                <div class="habit-label">平均观看时长</div>
                <div class="habit-value">{{ formatDuration(habits.avgWatchDuration) }}</div>
              </div>
              <div class="habit-item">
                <div class="habit-label">最喜欢的内容类型</div>
                <div class="habit-value">{{ habits.favoriteCategory }}</div>
              </div>
              <div class="habit-item">
                <div class="habit-label">观看频率</div>
                <div class="habit-value">{{ habits.watchFrequency }}</div>
              </div>
            </div>
          </el-card>
        </el-col>

        <!-- 内容偏好分析 -->
        <el-col :xs="24" :sm="24" :md="12" :lg="8">
          <el-card class="analysis-card">
            <template #header>
              <span>内容偏好</span>
            </template>
            <div class="preference-tags">
              <el-tag
                v-for="(tag, index) in preferences.tags"
                :key="index"
                :type="tagTypes[index % tagTypes.length]"
                :size="getTagSize(tag.weight)"
                effect="light"
                class="preference-tag"
              >
                {{ tag.name }} ({{ tag.count }})
              </el-tag>
            </div>
          </el-card>
        </el-col>

        <!-- AI 洞察 -->
        <el-col :xs="24" :sm="24" :md="12" :lg="8">
          <el-card class="analysis-card">
            <template #header>
              <div class="ai-header">
                <span>AI 洞察</span>
                <el-button @click="generateInsights" :loading="generatingInsights" size="small" type="primary">
                  <el-icon><Magic /></el-icon>
                  生成洞察
                </el-button>
              </div>
            </template>
            <div class="ai-insights">
              <div v-if="insights.length === 0 && !generatingInsights" class="no-insights">
                <el-empty description="暂无AI洞察" :image-size="80" />
                <p>点击"生成洞察"按钮获取个性化分析</p>
              </div>
              <div v-else class="insights-list">
                <div v-for="(insight, index) in insights" :key="index" class="insight-item">
                  <div class="insight-icon">
                    <el-icon><Lightbulb /></el-icon>
                  </div>
                  <div class="insight-content">
                    <div class="insight-title">{{ insight.title }}</div>
                    <div class="insight-description">{{ insight.description }}</div>
                  </div>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useDouyinStore } from '@/stores/douyin'
import { analysisAPI } from '@/utils/api'
import { ElMessage } from 'element-plus'
import { use } from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'
import { LineChart, BarChart, PieChart } from 'echarts/charts'
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent
} from 'echarts/components'
import VChart from 'vue-echarts'
import dayjs from 'dayjs'

// 注册 ECharts 组件
use([
  CanvasRenderer,
  LineChart,
  BarChart,
  PieChart,
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent
])

const douyinStore = useDouyinStore()

// 响应式数据
const loading = ref(false)
const generatingInsights = ref(false)
const timeRange = ref('30d')
const customDateRange = ref([])
const trendMetric = ref('count')

// 统计数据
const stats = reactive({
  totalVideos: 0,
  totalDuration: 0,
  avgEngagement: 0,
  uniqueAuthors: <AUTHORS>
  videosChange: 0,
  durationChange: 0,
  engagementChange: 0,
  authorsChange: 0
})

// 观看习惯
const habits = reactive({
  mostActiveTime: '20:00-22:00',
  avgWatchDuration: 0,
  favoriteCategory: '娱乐',
  watchFrequency: '每天3-5次'
})

// 内容偏好
const preferences = reactive({
  tags: []
})

// AI洞察
const insights = ref([])

// 标签类型和大小
const tagTypes = ['', 'success', 'info', 'warning', 'danger']

// 图表数据
const trendData = ref([])
const timeDistributionData = ref([])
const categoryData = ref([])
const authorData = ref([])

// 计算属性 - 趋势图表配置
const trendChartOption = computed(() => ({
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'cross'
    }
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    data: trendData.value.map(item => item.date),
    axisLine: {
      lineStyle: {
        color: '#e8e8e8'
      }
    }
  },
  yAxis: {
    type: 'value',
    axisLine: {
      lineStyle: {
        color: '#e8e8e8'
      }
    }
  },
  series: [{
    name: trendMetric.value === 'count' ? '视频数量' : '观看时长',
    type: 'line',
    data: trendData.value.map(item => trendMetric.value === 'count' ? item.count : item.duration),
    smooth: true,
    lineStyle: {
      color: '#FE2C55'
    },
    areaStyle: {
      color: {
        type: 'linear',
        x: 0,
        y: 0,
        x2: 0,
        y2: 1,
        colorStops: [{
          offset: 0, color: 'rgba(254, 44, 85, 0.3)'
        }, {
          offset: 1, color: 'rgba(254, 44, 85, 0.05)'
        }]
      }
    }
  }]
}))

// 时间分布图表配置
const timeDistributionOption = computed(() => ({
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow'
    }
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    data: timeDistributionData.value.map(item => item.hour + ':00'),
    axisLine: {
      lineStyle: {
        color: '#e8e8e8'
      }
    }
  },
  yAxis: {
    type: 'value',
    axisLine: {
      lineStyle: {
        color: '#e8e8e8'
      }
    }
  },
  series: [{
    name: '观看次数',
    type: 'bar',
    data: timeDistributionData.value.map(item => item.count),
    itemStyle: {
      color: {
        type: 'linear',
        x: 0,
        y: 0,
        x2: 0,
        y2: 1,
        colorStops: [{
          offset: 0, color: '#FE2C55'
        }, {
          offset: 1, color: '#FF5E78'
        }]
      }
    }
  }]
}))

// 内容类型分布图表配置
const categoryDistributionOption = computed(() => ({
  tooltip: {
    trigger: 'item',
    formatter: '{a} <br/>{b}: {c} ({d}%)'
  },
  legend: {
    orient: 'vertical',
    left: 'left'
  },
  series: [{
    name: '内容类型',
    type: 'pie',
    radius: ['40%', '70%'],
    avoidLabelOverlap: false,
    label: {
      show: false,
      position: 'center'
    },
    emphasis: {
      label: {
        show: true,
        fontSize: '18',
        fontWeight: 'bold'
      }
    },
    labelLine: {
      show: false
    },
    data: categoryData.value,
    itemStyle: {
      borderRadius: 8,
      borderColor: '#fff',
      borderWidth: 2
    }
  }]
}))

// 作者排行图表配置
const authorRankingOption = computed(() => ({
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow'
    }
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true
  },
  xAxis: {
    type: 'value',
    axisLine: {
      lineStyle: {
        color: '#e8e8e8'
      }
    }
  },
  yAxis: {
    type: 'category',
    data: authorData.value.map(item => item.name),
    axisLine: {
      lineStyle: {
        color: '#e8e8e8'
      }
    }
  },
  series: [{
    name: '观看次数',
    type: 'bar',
    data: authorData.value.map(item => item.count),
    itemStyle: {
      color: {
        type: 'linear',
        x: 0,
        y: 0,
        x2: 1,
        y2: 0,
        colorStops: [{
          offset: 0, color: '#84fab0'
        }, {
          offset: 1, color: '#8fd3f4'
        }]
      }
    }
  }]
}))

// 方法
const formatDuration = (seconds) => {
  if (!seconds) return '0分钟'
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)

  if (hours > 0) {
    return `${hours}小时${minutes}分钟`
  }
  return `${minutes}分钟`
}

const getTagSize = (weight) => {
  if (weight > 0.8) return 'large'
  if (weight > 0.5) return 'default'
  return 'small'
}

const handleTimeRangeChange = () => {
  customDateRange.value = []
  loadAnalysisData()
}

const handleCustomRangeChange = () => {
  if (customDateRange.value && customDateRange.value.length === 2) {
    timeRange.value = 'custom'
    loadAnalysisData()
  }
}

const handleRefresh = () => {
  loadAnalysisData()
}

const handleExport = async () => {
  try {
    const response = await analysisAPI.exportReport({
      timeRange: timeRange.value,
      customRange: customDateRange.value
    })

    if (response.success) {
      // 创建下载链接
      const url = window.URL.createObjectURL(new Blob([response.data]))
      const link = document.createElement('a')
      link.href = url
      link.download = `analysis-report-${dayjs().format('YYYY-MM-DD')}.pdf`
      link.click()
      window.URL.revokeObjectURL(url)

      ElMessage.success('报告导出成功')
    }
  } catch (error) {
    ElMessage.error('导出失败')
  }
}

const generateInsights = async () => {
  try {
    generatingInsights.value = true
    const response = await analysisAPI.generateInsights({
      timeRange: timeRange.value,
      customRange: customDateRange.value
    })

    if (response.success) {
      insights.value = response.data
      ElMessage.success('AI洞察生成成功')
    }
  } catch (error) {
    ElMessage.error('生成洞察失败')
  } finally {
    generatingInsights.value = false
  }
}

// 加载分析数据
const loadAnalysisData = async () => {
  try {
    loading.value = true

    const params = {
      timeRange: timeRange.value,
      customRange: customDateRange.value
    }

    // 加载统计数据
    const statsResponse = await analysisAPI.getStats(params)
    if (statsResponse.success) {
      Object.assign(stats, statsResponse.data)
    }

    // 加载趋势数据
    const trendResponse = await analysisAPI.getTrendData(params)
    if (trendResponse.success) {
      trendData.value = trendResponse.data
    }

    // 加载时间分布数据
    const timeResponse = await analysisAPI.getTimeDistribution(params)
    if (timeResponse.success) {
      timeDistributionData.value = timeResponse.data
    }

    // 加载分类数据
    const categoryResponse = await analysisAPI.getCategoryDistribution(params)
    if (categoryResponse.success) {
      categoryData.value = categoryResponse.data
    }

    // 加载作者数据
    const authorResponse = await analysisAPI.getAuthorRanking(params)
    if (authorResponse.success) {
      authorData.value = authorResponse.data
    }

    // 加载习惯数据
    const habitsResponse = await analysisAPI.getWatchHabits(params)
    if (habitsResponse.success) {
      Object.assign(habits, habitsResponse.data)
    }

    // 加载偏好数据
    const preferencesResponse = await analysisAPI.getPreferences(params)
    if (preferencesResponse.success) {
      preferences.tags = preferencesResponse.data.tags || []
    }

  } catch (error) {
    ElMessage.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

// 组件挂载时加载数据
onMounted(() => {
  loadAnalysisData()
})
</script>

<style lang="scss" scoped>
.analysis-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 16px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  flex-wrap: wrap;
  gap: 16px;
  
  .header-content {
    h1 {
      font-size: 28px;
      font-weight: 600;
      color: var(--text-primary);
      margin: 0 0 8px 0;
    }
    
    p {
      font-size: 16px;
      color: var(--text-secondary);
      margin: 0;
    }
  }
  
  .header-actions {
    display: flex;
    gap: 12px;
    
    .el-button {
      &.el-button--primary {
        background-color: var(--primary-color);
        border-color: var(--primary-color);
        
        &:hover, &:focus {
          background-color: var(--primary-light);
          border-color: var(--primary-light);
        }
      }
    }
  }
}

.time-range-section {
  margin-bottom: 24px;
  
  .range-card {
    border: none;
    border-radius: var(--border-radius-large);
    box-shadow: var(--shadow-light);
    overflow: hidden;
    
    :deep(.el-card__body) {
      padding: 16px;
    }
    
    .range-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      flex-wrap: wrap;
      gap: 16px;
      
      .range-selector {
        :deep(.el-radio-button__inner) {
          border-radius: 20px;
          padding: 8px 16px;
        }
        
        :deep(.el-radio-button:first-child .el-radio-button__inner) {
          border-radius: 20px 0 0 20px;
        }
        
        :deep(.el-radio-button:last-child .el-radio-button__inner) {
          border-radius: 0 20px 20px 0;
        }
        
        :deep(.el-radio-button__original) {
          opacity: 0;
        }
        
        :deep(.el-radio-button__inner:hover) {
          color: var(--primary-color);
        }
        
        :deep(.el-radio-button__orig-radio:checked + .el-radio-button__inner) {
          background-color: var(--primary-color);
          border-color: var(--primary-color);
          box-shadow: -1px 0 0 0 var(--primary-color);
        }
      }
    }
  }
}

.overview-section {
  margin-bottom: 32px;
  
  .overview-card {
    background-color: var(--bg-white);
    border-radius: var(--border-radius-large);
    padding: 20px;
    box-shadow: var(--shadow-light);
    display: flex;
    align-items: center;
    gap: 16px;
    height: 100%;
    transition: transform 0.2s;
    
    &:hover {
      transform: translateY(-5px);
    }
    
    .card-icon {
      width: 48px;
      height: 48px;
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 24px;
      color: white;
      
      &.videos {
        background: linear-gradient(135deg, var(--primary-color), #ff5050);
      }
      
      &.duration {
        background: linear-gradient(135deg, #ff9a9e, #fad0c4);
      }
      
      &.engagement {
        background: linear-gradient(135deg, #a18cd1, #fbc2eb);
      }
      
      &.authors {
        background: linear-gradient(135deg, #84fab0, #8fd3f4);
      }
    }
    
    .card-content {
      flex: 1;
      
      .card-value {
        font-size: 24px;
        font-weight: 600;
        color: var(--text-primary);
        line-height: 1;
      }
      
      .card-label {
        font-size: 12px;
        color: var(--text-secondary);
        margin: 4px 0;
      }
      
      .card-change {
        display: flex;
        align-items: center;
        font-size: 12px;
        
        &.positive {
          color: #67c23a;
        }
        
        &.negative {
          color: #f56c6c;
        }
        
        .el-icon {
          margin-right: 4px;
        }
      }
    }
  }
}

.charts-section {
  margin-bottom: 32px;
  
  .chart-card {
    border: none;
    border-radius: var(--border-radius-large);
    box-shadow: var(--shadow-light);
    margin-bottom: 24px;
    transition: transform 0.2s;
    height: 100%;
    
    &:hover {
      transform: translateY(-5px);
    }
    
    :deep(.el-card__header) {
      padding: 16px;
      border-bottom: 1px solid var(--border-lighter);
      
      .chart-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        
        span {
          font-size: 16px;
          font-weight: 500;
          color: var(--text-primary);
        }
      }
    }
    
    :deep(.el-card__body) {
      padding: 16px;
    }
    
    .chart-container {
      height: 300px;
    }
  }
}

.detailed-analysis {
  .analysis-card {
    border: none;
    border-radius: var(--border-radius-large);
    box-shadow: var(--shadow-light);
    margin-bottom: 24px;
    transition: transform 0.2s;
    height: 100%;
    
    &:hover {
      transform: translateY(-5px);
    }
    
    :deep(.el-card__header) {
      padding: 16px;
      border-bottom: 1px solid var(--border-lighter);
      
      span {
        font-size: 16px;
        font-weight: 500;
        color: var(--text-primary);
      }
    }
    
    :deep(.el-card__body) {
      padding: 16px;
    }
    
    .habit-analysis {
      .habit-item {
        display: flex;
        justify-content: space-between;
        padding: 12px 0;
        border-bottom: 1px solid var(--border-lighter);
        
        &:last-child {
          border-bottom: none;
        }
        
        .habit-label {
          color: var(--text-secondary);
        }
        
        .habit-value {
          color: var(--text-primary);
          font-weight: 500;
        }
      }
    }
    
    .preference-tags {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
      
      .preference-tag {
        margin: 0;
        cursor: pointer;
        transition: transform 0.2s;
        
        &:hover {
          transform: scale(1.05);
        }
      }
    }
    
    .ai-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    
    .ai-insights {
      .no-insights {
        text-align: center;
        padding: 40px 0;
        
        p {
          color: var(--text-secondary);
          margin-top: 16px;
        }
      }
      
      .insights-list {
        .insight-item {
          display: flex;
          gap: 12px;
          padding: 16px 0;
          border-bottom: 1px solid var(--border-extra-light);
          
          &:last-child {
            border-bottom: none;
          }
          
          .insight-icon {
            width: 32px;
            height: 32px;
            border-radius: 8px;
            background: linear-gradient(135deg, #ffd89b, #19547b);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            flex-shrink: 0;
          }
          
          .insight-content {
            flex: 1;
            
            .insight-title {
              font-weight: 500;
              color: var(--text-primary);
              margin-bottom: 4px;
            }
            
            .insight-description {
              font-size: 14px;
              color: var(--text-secondary);
              line-height: 1.5;
            }
          }
        }
      }
    }
  }
}

.top-content-section {
  margin-bottom: 32px;
  
  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    
    h3 {
      font-size: 18px;
      font-weight: 600;
      color: var(--text-primary);
      margin: 0;
    }
  }
  
  .top-content-list {
    .content-card {
      border: none;
      border-radius: var(--border-radius-large);
      box-shadow: var(--shadow-light);
      margin-bottom: 16px;
      transition: transform 0.2s;
      cursor: pointer;
      
      &:hover {
        transform: translateY(-5px);
      }
      
      :deep(.el-card__body) {
        padding: 0;
      }
      
      .content-item {
        display: flex;
        
        .content-cover {
          width: 120px;
          height: 160px;
          flex-shrink: 0;
          
          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
        }
        
        .content-info {
          flex: 1;
          padding: 16px;
          
          .content-title {
            font-size: 16px;
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: 8px;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
          }
          
          .content-meta {
            display: flex;
            align-items: center;
            gap: 16px;
            margin-bottom: 12px;
            
            .meta-item {
              display: flex;
              align-items: center;
              gap: 4px;
              font-size: 12px;
              color: var(--text-secondary);
              
              .el-icon {
                font-size: 14px;
              }
            }
          }
          
          .content-stats {
            display: flex;
            gap: 16px;
            
            .stat-item {
              .stat-value {
                font-size: 18px;
                font-weight: 600;
                color: var(--text-primary);
              }
              
              .stat-label {
                font-size: 12px;
                color: var(--text-secondary);
              }
            }
          }
        }
      }
    }
  }
}

// 响应式
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    
    .header-actions {
      width: 100%;
      justify-content: space-between;
    }
  }
  
  .range-content {
    flex-direction: column;
    align-items: flex-start !important;
    
    .range-selector {
      width: 100%;
      overflow-x: auto;
      white-space: nowrap;
    }
    
    .custom-range {
      width: 100%;
      
      .el-date-picker {
        width: 100%;
      }
    }
  }
  
  .content-item {
    flex-direction: column;
    
    .content-cover {
      width: 100% !important;
      height: 200px !important;
    }
  }
}

// 暗色主题适配
.dark {
  .chart-card, .analysis-card {
    :deep(.echarts) {
      filter: brightness(0.9);
    }
  }
}
</style>