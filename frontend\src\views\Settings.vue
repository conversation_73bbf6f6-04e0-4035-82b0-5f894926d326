<template>
  <div class="settings-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h1>设置</h1>
        <p>管理您的账号和系统设置</p>
      </div>
    </div>

    <!-- 设置选项卡 -->
    <div class="settings-tabs">
      <el-tabs v-model="activeTab" type="border-card">
        <!-- 账号设置 -->
        <el-tab-pane label="账号设置" name="account">
          <div class="tab-content">
            <!-- 抖音账号绑定 -->
            <div class="setting-section">
              <div class="section-header">
                <h3>抖音账号绑定</h3>
                <p>绑定您的抖音账号以同步历史记录</p>
              </div>

              <div class="douyin-auth">
                <div v-if="!authStore.hasDouyinAuth" class="auth-empty">
                  <div class="auth-info">
                    <el-icon class="auth-icon"><User /></el-icon>
                    <h4>未绑定抖音账号</h4>
                    <p>请扫描二维码登录您的抖音账号</p>
                  </div>

                  <div class="qr-section">
                    <div v-if="qrCode" class="qr-code">
                      <img :src="qrCode" alt="登录二维码" />
                      <div class="qr-status">
                        <el-icon v-if="qrStatus === 'loading'"><Loading /></el-icon>
                        <span>{{ qrStatusText }}</span>
                      </div>
                    </div>

                    <div class="qr-actions">
                      <el-button @click="generateQRCode" :loading="generatingQR" type="primary">
                        <el-icon><Refresh /></el-icon>
                        {{ qrCode ? '刷新二维码' : '生成二维码' }}
                      </el-button>
                    </div>
                  </div>
                </div>

                <div v-else class="auth-success">
                  <div class="user-info">
                    <img :src="authStore.douyinUser.avatar || '/default-avatar.jpg'" alt="头像" class="user-avatar" />
                    <div class="user-details">
                      <h4>{{ authStore.douyinUser.nickname }}</h4>
                      <p>抖音号：{{ authStore.douyinUser.unique_id || authStore.douyinUser.sec_uid }}</p>
                      <p>绑定时间：{{ formatTime(authStore.douyinUser.bind_time) }}</p>
                    </div>
                  </div>

                  <div class="auth-actions">
                    <el-button @click="handleUnbind" type="danger" plain>
                      <el-icon><Close /></el-icon>
                      解除绑定
                    </el-button>
                    <el-button @click="handleSyncHistory" :loading="syncing" type="primary">
                      <el-icon><Refresh /></el-icon>
                      同步历史记录
                    </el-button>
                  </div>
                </div>
              </div>
            </div>

            <!-- 用户信息 -->
            <div class="setting-section">
              <div class="section-header">
                <h3>用户信息</h3>
                <p>管理您的个人信息</p>
              </div>

              <el-form :model="userForm" label-width="100px" class="user-form">
                <el-form-item label="用户名">
                  <el-input v-model="userForm.username" placeholder="请输入用户名" />
                </el-form-item>

                <el-form-item label="邮箱">
                  <el-input v-model="userForm.email" placeholder="请输入邮箱" />
                </el-form-item>

                <el-form-item label="手机号">
                  <el-input v-model="userForm.phone" placeholder="请输入手机号" />
                </el-form-item>

                <el-form-item>
                  <el-button @click="handleSaveUserInfo" :loading="saving" type="primary">
                    保存
                  </el-button>
                </el-form-item>
              </el-form>
            </div>
          </div>
        </el-tab-pane>

        <!-- 系统设置 -->
        <el-tab-pane label="系统设置" name="system">
          <div class="tab-content">
            <!-- 主题设置 -->
            <div class="setting-section">
              <div class="section-header">
                <h3>主题设置</h3>
                <p>选择您喜欢的界面主题</p>
              </div>

              <div class="theme-options">
                <div class="theme-item" :class="{ active: themeStore.theme === 'light' }" @click="handleThemeChange('light')">
                  <div class="theme-preview light">
                    <div class="preview-header"></div>
                    <div class="preview-content">
                      <div class="preview-sidebar"></div>
                      <div class="preview-main"></div>
                    </div>
                  </div>
                  <div class="theme-name">浅色主题</div>
                </div>

                <div class="theme-item" :class="{ active: themeStore.theme === 'dark' }" @click="handleThemeChange('dark')">
                  <div class="theme-preview dark">
                    <div class="preview-header"></div>
                    <div class="preview-content">
                      <div class="preview-sidebar"></div>
                      <div class="preview-main"></div>
                    </div>
                  </div>
                  <div class="theme-name">深色主题</div>
                </div>

                <div class="theme-item" :class="{ active: themeStore.theme === 'auto' }" @click="handleThemeChange('auto')">
                  <div class="theme-preview auto">
                    <div class="preview-header"></div>
                    <div class="preview-content">
                      <div class="preview-sidebar"></div>
                      <div class="preview-main"></div>
                    </div>
                  </div>
                  <div class="theme-name">跟随系统</div>
                </div>
              </div>
            </div>

            <!-- 下载设置 -->
            <div class="setting-section">
              <div class="section-header">
                <h3>下载设置</h3>
                <p>配置视频下载相关选项</p>
              </div>

              <el-form :model="downloadSettings" label-width="120px" class="download-form">
                <el-form-item label="下载目录">
                  <el-input v-model="downloadSettings.downloadPath" placeholder="选择下载目录">
                    <template #append>
                      <el-button @click="handleSelectPath">
                        <el-icon><Folder /></el-icon>
                        选择
                      </el-button>
                    </template>
                  </el-input>
                </el-form-item>

                <el-form-item label="默认视频质量">
                  <el-select v-model="downloadSettings.defaultQuality" placeholder="选择默认质量">
                    <el-option label="原画" value="origin" />
                    <el-option label="超清 (1080p)" value="1080p" />
                    <el-option label="高清 (720p)" value="720p" />
                    <el-option label="标清 (480p)" value="480p" />
                  </el-select>
                </el-form-item>

                <el-form-item label="同时下载数">
                  <el-input-number v-model="downloadSettings.maxConcurrent" :min="1" :max="10" />
                </el-form-item>

                <el-form-item label="自动重试">
                  <el-switch v-model="downloadSettings.autoRetry" />
                </el-form-item>

                <el-form-item label="下载完成提醒">
                  <el-switch v-model="downloadSettings.notification" />
                </el-form-item>

                <el-form-item>
                  <el-button @click="handleSaveDownloadSettings" :loading="saving" type="primary">
                    保存设置
                  </el-button>
                </el-form-item>
              </el-form>
            </div>

            <!-- 数据设置 -->
            <div class="setting-section">
              <div class="section-header">
                <h3>数据设置</h3>
                <p>管理您的数据和隐私</p>
              </div>

              <div class="data-actions">
                <div class="action-item">
                  <div class="action-info">
                    <h4>导出数据</h4>
                    <p>导出您的历史记录和分析数据</p>
                  </div>
                  <el-button @click="handleExportData" :loading="exporting" type="primary">
                    <el-icon><Download /></el-icon>
                    导出
                  </el-button>
                </div>

                <div class="action-item">
                  <div class="action-info">
                    <h4>清空历史记录</h4>
                    <p>删除所有观看历史记录（不可恢复）</p>
                  </div>
                  <el-button @click="handleClearHistory" type="danger" plain>
                    <el-icon><Delete /></el-icon>
                    清空
                  </el-button>
                </div>

                <div class="action-item">
                  <div class="action-info">
                    <h4>重置所有设置</h4>
                    <p>将所有设置恢复为默认值</p>
                  </div>
                  <el-button @click="handleResetSettings" type="warning" plain>
                    <el-icon><Refresh /></el-icon>
                    重置
                  </el-button>
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>

        <!-- 关于 -->
        <el-tab-pane label="关于" name="about">
          <div class="tab-content">
            <div class="about-section">
              <div class="app-info">
                <div class="app-logo">
                  <img src="/logo.png" alt="应用图标" />
                </div>
                <div class="app-details">
                  <h2>抖音历史记录分析工具</h2>
                  <p class="version">版本 {{ appVersion }}</p>
                  <p class="description">
                    一个专业的抖音观看历史记录分析工具，帮助您深入了解自己的观看习惯和内容偏好。
                  </p>
                </div>
              </div>

              <div class="features-list">
                <h3>主要功能</h3>
                <ul>
                  <li>同步抖音观看历史记录</li>
                  <li>智能数据分析和可视化</li>
                  <li>视频批量下载管理</li>
                  <li>AI 驱动的内容洞察</li>
                  <li>个性化推荐分析</li>
                </ul>
              </div>

              <div class="contact-info">
                <h3>联系我们</h3>
                <p>如果您有任何问题或建议，请联系我们：</p>
                <div class="contact-links">
                  <el-button type="primary" link>
                    <el-icon><Message /></el-icon>
                    <EMAIL>
                  </el-button>
                  <el-button type="primary" link>
                    <el-icon><Link /></el-icon>
                    GitHub
                  </el-button>
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { useThemeStore } from '@/stores/theme'
import { useDouyinStore } from '@/stores/douyin'
import { settingsAPI, authAPI } from '@/utils/api'
import { ElMessage, ElMessageBox } from 'element-plus'
import dayjs from 'dayjs'

const authStore = useAuthStore()
const themeStore = useThemeStore()
const douyinStore = useDouyinStore()

// 响应式数据
const activeTab = ref('account')
const generatingQR = ref(false)
const qrCode = ref('')
const qrStatus = ref('waiting')
const qrToken = ref('')
const qrTimer = ref(null)
const syncing = ref(false)
const saving = ref(false)
const exporting = ref(false)
const appVersion = ref('1.0.0')

// 表单数据
const userForm = reactive({
  username: '',
  email: '',
  phone: ''
})

const downloadSettings = reactive({
  downloadPath: '',
  defaultQuality: '720p',
  maxConcurrent: 3,
  autoRetry: true,
  notification: true
})

// 计算属性
const qrStatusText = computed(() => {
  switch (qrStatus.value) {
    case 'waiting':
      return '请使用抖音APP扫描二维码'
    case 'scanned':
      return '已扫描，请在手机上确认登录'
    case 'confirmed':
      return '登录成功'
    case 'expired':
      return '二维码已过期，请刷新'
    case 'error':
      return '登录失败，请重试'
    default:
      return '加载中...'
  }
})

// 方法
const formatTime = (time) => {
  return dayjs(time).format('YYYY-MM-DD HH:mm:ss')
}

// 生成二维码
const generateQRCode = async () => {
  try {
    generatingQR.value = true
    const response = await authAPI.getQRCode()

    if (response.success) {
      qrCode.value = response.data.qr_code
      qrToken.value = response.data.token
      qrStatus.value = 'waiting'

      // 开始轮询二维码状态
      startQRPolling()
    }
  } catch (error) {
    ElMessage.error('生成二维码失败')
  } finally {
    generatingQR.value = false
  }
}

// 开始轮询二维码状态
const startQRPolling = () => {
  if (qrTimer.value) {
    clearInterval(qrTimer.value)
  }

  qrTimer.value = setInterval(async () => {
    try {
      const response = await authAPI.checkQRStatus(qrToken.value)

      if (response.success) {
        qrStatus.value = response.data.status

        if (response.data.status === 'confirmed') {
          // 登录成功
          await authStore.setDouyinAuth(response.data.user_info)
          ElMessage.success('抖音账号绑定成功')
          stopQRPolling()
        } else if (response.data.status === 'expired' || response.data.status === 'error') {
          // 二维码过期或错误
          stopQRPolling()
        }
      }
    } catch (error) {
      console.error('检查二维码状态失败:', error)
    }
  }, 2000)
}

// 停止轮询二维码状态
const stopQRPolling = () => {
  if (qrTimer.value) {
    clearInterval(qrTimer.value)
    qrTimer.value = null
  }
}

// 解除绑定
const handleUnbind = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要解除抖音账号绑定吗？解除后将无法同步历史记录。',
      '解除绑定',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const response = await authAPI.unbindDouyin()
    if (response.success) {
      await authStore.clearDouyinAuth()
      ElMessage.success('已解除绑定')
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('解除绑定失败')
    }
  }
}

// 同步历史记录
const handleSyncHistory = async () => {
  syncing.value = true
  await douyinStore.syncHistory()
  syncing.value = false
}

// 保存用户信息
const handleSaveUserInfo = async () => {
  try {
    saving.value = true
    const response = await settingsAPI.updateUserInfo(userForm)

    if (response.success) {
      ElMessage.success('用户信息保存成功')
    }
  } catch (error) {
    ElMessage.error('保存失败')
  } finally {
    saving.value = false
  }
}

// 主题切换
const handleThemeChange = (theme) => {
  themeStore.setTheme(theme)
  ElMessage.success(`已切换到${theme === 'light' ? '浅色' : theme === 'dark' ? '深色' : '自动'}主题`)
}

// 选择下载路径
const handleSelectPath = async () => {
  try {
    const response = await settingsAPI.selectDownloadPath()
    if (response.success) {
      downloadSettings.downloadPath = response.data.path
    }
  } catch (error) {
    ElMessage.error('选择路径失败')
  }
}

// 保存下载设置
const handleSaveDownloadSettings = async () => {
  try {
    saving.value = true
    const response = await settingsAPI.updateDownloadSettings(downloadSettings)

    if (response.success) {
      ElMessage.success('下载设置保存成功')
    }
  } catch (error) {
    ElMessage.error('保存失败')
  } finally {
    saving.value = false
  }
}

// 导出数据
const handleExportData = async () => {
  try {
    exporting.value = true
    const response = await settingsAPI.exportData()

    if (response.success) {
      // 创建下载链接
      const url = window.URL.createObjectURL(new Blob([response.data]))
      const link = document.createElement('a')
      link.href = url
      link.download = `douyin-data-${dayjs().format('YYYY-MM-DD')}.json`
      link.click()
      window.URL.revokeObjectURL(url)

      ElMessage.success('数据导出成功')
    }
  } catch (error) {
    ElMessage.error('导出失败')
  } finally {
    exporting.value = false
  }
}

// 清空历史记录
const handleClearHistory = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要清空所有历史记录吗？此操作不可恢复！',
      '清空确认',
      {
        confirmButtonText: '确定清空',
        cancelButtonText: '取消',
        type: 'error'
      }
    )

    const response = await settingsAPI.clearHistory()
    if (response.success) {
      douyinStore.clearHistory()
      ElMessage.success('历史记录已清空')
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('清空失败')
    }
  }
}

// 重置设置
const handleResetSettings = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要重置所有设置吗？此操作将恢复默认设置。',
      '重置确认',
      {
        confirmButtonText: '确定重置',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const response = await settingsAPI.resetSettings()
    if (response.success) {
      // 重新加载设置
      await loadSettings()
      ElMessage.success('设置已重置')
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('重置失败')
    }
  }
}

// 加载设置
const loadSettings = async () => {
  try {
    // 加载用户信息
    const userResponse = await settingsAPI.getUserInfo()
    if (userResponse.success) {
      Object.assign(userForm, userResponse.data)
    }

    // 加载下载设置
    const downloadResponse = await settingsAPI.getDownloadSettings()
    if (downloadResponse.success) {
      Object.assign(downloadSettings, downloadResponse.data)
    }

    // 加载应用版本
    const versionResponse = await settingsAPI.getAppVersion()
    if (versionResponse.success) {
      appVersion.value = versionResponse.data.version
    }
  } catch (error) {
    console.error('加载设置失败:', error)
  }
}

// 组件挂载时
onMounted(() => {
  loadSettings()
})

// 组件卸载时
onUnmounted(() => {
  stopQRPolling()
})
</script>

<style lang="scss" scoped>
.settings-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 16px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  
  .header-content {
    h1 {
      font-size: 28px;
      font-weight: 600;
      color: var(--text-primary);
      margin: 0 0 8px 0;
    }
    
    p {
      font-size: 16px;
      color: var(--text-secondary);
      margin: 0;
    }
  }
}

.settings-tabs {
  :deep(.el-tabs) {
    box-shadow: var(--shadow-light);
    border-radius: var(--border-radius-large);
    overflow: hidden;
    
    .el-tabs__header {
      margin: 0;
      
      .el-tabs__nav {
        border: none;
      }
      
      .el-tabs__item {
        height: 56px;
        line-height: 56px;
        font-size: 16px;
        color: var(--text-secondary);
        border: none;
        transition: all 0.3s;
        
        &.is-active {
          color: var(--primary-color);
          font-weight: 500;
        }
        
        &:hover {
          color: var(--primary-color);
        }
      }
      
      .el-tabs__active-bar {
        background-color: var(--primary-color);
        height: 3px;
      }
    }
    
    .el-tabs__content {
      padding: 0;
      
      .el-tab-pane {
        padding: 0;
      }
    }
  }
  
  .tab-content {
    padding: 24px;
  }
  
  .setting-section {
    margin-bottom: 40px;
    
    &:last-child {
      margin-bottom: 0;
    }
    
    .section-header {
      margin-bottom: 24px;
      
      h3 {
        font-size: 18px;
        font-weight: 600;
        color: var(--text-primary);
        margin: 0 0 8px 0;
      }
      
      p {
        font-size: 14px;
        color: var(--text-secondary);
        margin: 0;
      }
    }
    
    .douyin-auth {
      background-color: var(--bg-white);
      border-radius: var(--border-radius-large);
      padding: 24px;
      box-shadow: var(--shadow-light);
      
      .auth-empty {
        display: flex;
        flex-wrap: wrap;
        gap: 32px;
        align-items: center;
        
        .auth-info {
          flex: 1;
          min-width: 200px;
          text-align: center;
          
          .auth-icon {
            font-size: 48px;
            color: var(--text-placeholder);
            margin-bottom: 16px;
          }
          
          h4 {
            font-size: 18px;
            font-weight: 500;
            color: var(--text-primary);
            margin: 0 0 8px 0;
          }
          
          p {
            font-size: 14px;
            color: var(--text-secondary);
            margin: 0;
          }
        }
        
        .qr-section {
          flex: 1;
          min-width: 200px;
          display: flex;
          flex-direction: column;
          align-items: center;
          
          .qr-code {
            width: 200px;
            height: 200px;
            background-color: white;
            border-radius: var(--border-radius-base);
            padding: 16px;
            box-shadow: var(--shadow-light);
            margin-bottom: 16px;
            
            img {
              width: 100%;
              height: 100%;
              object-fit: contain;
            }
            
            .qr-status {
              margin-top: 8px;
              text-align: center;
              font-size: 14px;
              color: var(--text-secondary);
              display: flex;
              align-items: center;
              justify-content: center;
              gap: 8px;
            }
          }
          
          .qr-actions {
            .el-button {
              background-color: var(--primary-color);
              border-color: var(--primary-color);
              
              &:hover, &:focus {
                background-color: var(--primary-light);
                border-color: var(--primary-light);
              }
            }
          }
        }
      }
      
      .auth-success {
        display: flex;
        flex-wrap: wrap;
        gap: 24px;
        align-items: center;
        
        .user-info {
          flex: 1;
          min-width: 200px;
          display: flex;
          align-items: center;
          gap: 16px;
          
          .user-avatar {
            width: 64px;
            height: 64px;
            border-radius: 50%;
            object-fit: cover;
            border: 2px solid var(--primary-color);
          }
          
          .user-details {
            h4 {
              font-size: 18px;
              font-weight: 500;
              color: var(--text-primary);
              margin: 0 0 8px 0;
            }
            
            p {
              font-size: 14px;
              color: var(--text-secondary);
              margin: 0 0 4px 0;
              
              &:last-child {
                margin-bottom: 0;
              }
            }
          }
        }
        
        .auth-actions {
          display: flex;
          gap: 12px;
          
          .el-button {
            &.el-button--primary {
              background-color: var(--primary-color);
              border-color: var(--primary-color);
              
              &:hover, &:focus {
                background-color: var(--primary-light);
                border-color: var(--primary-light);
              }
            }
          }
        }
      }
    }
    
    .user-form {
      max-width: 500px;
      
      :deep(.el-input__wrapper) {
        border-radius: var(--border-radius-base);
        
        &.is-focus {
          box-shadow: 0 0 0 1px var(--primary-color) inset;
        }
      }
      
      :deep(.el-button--primary) {
        background-color: var(--primary-color);
        border-color: var(--primary-color);
        
        &:hover, &:focus {
          background-color: var(--primary-light);
          border-color: var(--primary-light);
        }
      }
    }
    
    .theme-options {
      display: flex;
      flex-wrap: wrap;
      gap: 24px;
      
      .theme-item {
        width: 160px;
        cursor: pointer;
        transition: transform 0.2s;
        
        &:hover {
          transform: translateY(-5px);
        }
        
        &.active {
          .theme-preview {
            border: 2px solid var(--primary-color);
          }
          
          .theme-name {
            color: var(--primary-color);
            font-weight: 500;
          }
        }
        
        .theme-preview {
          height: 100px;
          border-radius: var(--border-radius-base);
          overflow: hidden;
          border: 2px solid transparent;
          transition: all 0.3s;
          margin-bottom: 8px;
          
          &.light {
            background-color: #ffffff;
            
            .preview-header {
              background-color: #ffffff;
              border-bottom: 1px solid #e8e8e8;
            }
            
            .preview-sidebar {
              background-color: #f8f8f8;
            }
            
            .preview-main {
              background-color: #ffffff;
            }
          }
          
          &.dark {
            background-color: #121212;
            
            .preview-header {
              background-color: #1e1e1e;
              border-bottom: 1px solid #333333;
            }
            
            .preview-sidebar {
              background-color: #1e1e1e;
            }
            
            .preview-main {
              background-color: #121212;
            }
          }
          
          &.auto {
            background: linear-gradient(to right, #ffffff 50%, #121212 50%);
            
            .preview-header {
              background: linear-gradient(to right, #ffffff 50%, #1e1e1e 50%);
              border-bottom: 1px solid #e8e8e8;
            }
            
            .preview-content {
              background: linear-gradient(to right, #ffffff 50%, #121212 50%);
            }
          }
          
          .preview-header {
            height: 20%;
          }
          
          .preview-content {
            height: 80%;
            display: flex;
            
            .preview-sidebar {
              width: 30%;
              height: 100%;
            }
            
            .preview-main {
              width: 70%;
              height: 100%;
            }
          }
        }
        
        .theme-name {
          text-align: center;
          font-size: 14px;
          color: var(--text-secondary);
        }
      }
    }
    
    .download-form {
      max-width: 500px;
      
      :deep(.el-input__wrapper) {
        border-radius: var(--border-radius-base);
        
        &.is-focus {
          box-shadow: 0 0 0 1px var(--primary-color) inset;
        }
      }
      
      :deep(.el-input-number__decrease),
      :deep(.el-input-number__increase) {
        &:hover {
          color: var(--primary-color);
        }
      }
      
      :deep(.el-switch.is-checked .el-switch__core) {
        background-color: var(--primary-color);
        border-color: var(--primary-color);
      }
      
      :deep(.el-button--primary) {
        background-color: var(--primary-color);
        border-color: var(--primary-color);
        
        &:hover, &:focus {
          background-color: var(--primary-light);
          border-color: var(--primary-light);
        }
      }
    }
    
    .data-actions {
      .action-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        background-color: var(--bg-white);
        border-radius: var(--border-radius-large);
        padding: 20px;
        box-shadow: var(--shadow-light);
        margin-bottom: 16px;
        transition: transform 0.2s;
        
        &:hover {
          transform: translateY(-5px);
        }
        
        &:last-child {
          margin-bottom: 0;
        }
        
        .action-info {
          h4 {
            font-size: 16px;
            font-weight: 500;
            color: var(--text-primary);
            margin: 0 0 8px 0;
          }
          
          p {
            font-size: 14px;
            color: var(--text-secondary);
            margin: 0;
          }
        }
        
        .el-button {
          &.el-button--primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
            
            &:hover, &:focus {
              background-color: var(--primary-light);
              border-color: var(--primary-light);
            }
          }
        }
      }
    }
    
    .about-section {
      text-align: center;
      
      .app-info {
        margin-bottom: 40px;
        
        .app-logo {
          width: 100px;
          height: 100px;
          margin: 0 auto 20px;
          
          img {
            width: 100%;
            height: 100%;
            object-fit: contain;
          }
        }
        
        .app-name {
          font-size: 24px;
          font-weight: 600;
          color: var(--text-primary);
          margin: 0 0 8px 0;
        }
        
        .app-version {
          font-size: 14px;
          color: var(--text-secondary);
          margin: 0 0 16px 0;
        }
        
        .app-description {
          font-size: 16px;
          color: var(--text-regular);
          max-width: 600px;
          margin: 0 auto;
          line-height: 1.6;
        }
      }
      
      .app-links {
        display: flex;
        justify-content: center;
        gap: 16px;
        margin-bottom: 40px;
        
        .link-item {
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: 8px;
          
          .link-icon {
            width: 48px;
            height: 48px;
            border-radius: 12px;
            background-color: var(--primary-color);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            transition: transform 0.2s;
            
            &:hover {
              transform: scale(1.1);
            }
          }
          
          .link-name {
            font-size: 14px;
            color: var(--text-secondary);
          }
        }
      }
      
      .app-team {
        .team-title {
          font-size: 18px;
          font-weight: 500;
          color: var(--text-primary);
          margin: 0 0 20px 0;
        }
        
        .team-members {
          display: flex;
          justify-content: center;
          flex-wrap: wrap;
          gap: 24px;
          
          .member-item {
            width: 120px;
            
            .member-avatar {
              width: 80px;
              height: 80px;
              border-radius: 50%;
              margin: 0 auto 12px;
              overflow: hidden;
              border: 2px solid var(--primary-color);
              
              img {
                width: 100%;
                height: 100%;
                object-fit: cover;
              }
            }
            
            .member-name {
              font-size: 16px;
              font-weight: 500;
              color: var(--text-primary);
              margin: 0 0 4px 0;
            }
            
            .member-role {
              font-size: 12px;
              color: var(--text-secondary);
              margin: 0;
            }
          }
        }
      }
    }
  }
}

// 响应式
@media (max-width: 768px) {
  .auth-empty, .auth-success {
    flex-direction: column;
    text-align: center;
    
    .user-info {
      flex-direction: column;
      text-align: center;
    }
    
    .auth-actions {
      width: 100%;
      justify-content: center;
    }
  }
  
  .theme-options {
    justify-content: center;
  }
  
  .action-item {
    flex-direction: column;
    text-align: center;
    gap: 16px;
  }
  
  .app-links {
    flex-wrap: wrap;
  }
}

// 暗色主题适配
.dark {
  .douyin-auth {
    .qr-code {
      background-color: #2a2a2a;
    }
  }
  
  .theme-item {
    .theme-preview {
      &.light {
        border: 2px solid #333333;
      }
    }
  }
}
</style>
