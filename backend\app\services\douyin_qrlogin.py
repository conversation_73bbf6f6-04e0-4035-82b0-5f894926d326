import httpx
import json
import time
import random
import string
from typing import Dict, Optional, Any
from ..core.config import settings


class DouyinQRLoginService:
    """抖音扫码登录服务类"""
    
    def __init__(self):
        # 二维码登录相关URL
        self.qrcode_url = "https://sso.douyin.com/get_qrcode/"
        self.check_url = "https://sso.douyin.com/check_qrcode/"
        
        # 存储二维码token和状态
        self.qr_tokens = {}
        
        # 创建HTTP客户端
        self.client = httpx.AsyncClient(
            timeout=30.0,
            follow_redirects=True
        )
        
        # 请求头
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Referer': 'https://www.douyin.com/'
        }
    
    async def __aenter__(self):
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.client.aclose()
    
    def _generate_fp(self) -> str:
        """生成fp参数"""
        chars = string.ascii_lowercase + string.digits + '_'
        return ''.join(random.choice(chars) for _ in range(36))
    
    def _clean_expired_tokens(self, expiry_seconds: int = 300):
        """清理过期的token"""
        now = time.time()
        expired_tokens = []
        
        for token, data in self.qr_tokens.items():
            if now - data["created_at"] > expiry_seconds:
                expired_tokens.append(token)
                
        for token in expired_tokens:
            if token in self.qr_tokens:
                self.qr_tokens.pop(token)
    
    async def generate_qrcode(self) -> Dict[str, Any]:
        """生成抖音登录二维码"""
        # 清理过期token
        self._clean_expired_tokens()
        
        # 构建请求URL
        fp = self._generate_fp()
        params = {
            'next': 'https://www.douyin.com/',
            'aid': '6383',  # 抖音网页版的aid
            'service': 'https://www.douyin.com/',
            'is_vcd': '1',
            'fp': fp
        }
        
        try:
            # 请求二维码
            response = await self.client.get(self.qrcode_url, params=params, headers=self.headers)
            response.raise_for_status()
            data = response.json()
            
            if data.get('error_code') != 0:
                raise Exception(f"获取二维码失败: {data.get('message', '未知错误')}")
            
            qr_data = data.get('data', {})
            token = qr_data.get('token')
            qrcode = qr_data.get('qrcode')  # base64编码的二维码图片
            
            # 存储token信息
            self.qr_tokens[token] = {
                "status": "waiting",
                "created_at": time.time(),
                "user_info": None,
                "fp": fp
            }
            
            return {
                "qr_code": f"data:image/png;base64,{qrcode}",
                "token": token,
                "expires_in": 300  # 5分钟过期
            }
            
        except httpx.HTTPError as e:
            raise Exception(f"请求失败: {str(e)}")
        except json.JSONDecodeError:
            raise Exception("响应格式错误")
    
    async def check_qrcode_status(self, token: str) -> Dict[str, Any]:
        """检查二维码状态"""
        if token not in self.qr_tokens:
            return {"status": "expired"}
        
        # 检查是否过期
        now = time.time()
        if now - self.qr_tokens[token]["created_at"] > 300:  # 5分钟过期
            self.qr_tokens.pop(token)
            return {"status": "expired"}
        
        try:
            # 构建请求参数
            params = {
                'token': token,
                'service': 'https://www.douyin.com/'
            }
            
            # 请求检查状态
            response = await self.client.get(self.check_url, params=params, headers=self.headers)
            response.raise_for_status()
            data = response.json()
            
            # 根据返回状态更新本地状态
            if data.get('error_code') == 0:
                redirect_url = data.get('data', {}).get('redirect_url', '')
                
                # 已扫描但未确认
                if data.get('data', {}).get('status') == 1:
                    self.qr_tokens[token]["status"] = "scanned"
                    return {"status": "scanned"}
                
                # 已确认，获取cookie和用户信息
                elif data.get('data', {}).get('status') == 2 and redirect_url:
                    # 获取重定向URL中的cookie信息
                    cookies = await self._get_cookies_from_redirect(redirect_url)
                    
                    if cookies:
                        # 获取用户信息
                        user_info = await self._get_user_info(cookies)
                        
                        # 更新token状态
                        self.qr_tokens[token]["status"] = "confirmed"
                        self.qr_tokens[token]["user_info"] = {
                            "cookies": cookies,
                            "user_info": user_info
                        }
                        
                        return {
                            "status": "confirmed",
                            "user_info": {
                                "cookies": cookies,
                                "user_info": user_info
                            }
                        }
                    else:
                        return {"status": "error", "message": "获取Cookie失败"}
                
                # 二维码已过期
                elif data.get('data', {}).get('status') == 3:
                    self.qr_tokens.pop(token, None)
                    return {"status": "expired"}
                
                # 其他状态
                else:
                    return {"status": "waiting"}
            else:
                # API返回错误
                return {"status": "error", "message": data.get('message', '未知错误')}
            
        except httpx.HTTPError as e:
            return {"status": "error", "message": f"请求失败: {str(e)}"}
        except json.JSONDecodeError:
            return {"status": "error", "message": "响应格式错误"}
        except Exception as e:
            return {"status": "error", "message": str(e)}
    
    async def _get_cookies_from_redirect(self, redirect_url: str) -> Dict[str, str]:
        """从重定向URL获取Cookie"""
        try:
            # 发送请求获取Cookie
            response = await self.client.get(redirect_url, headers=self.headers, follow_redirects=False)
            
            # 提取所有Cookie
            cookies = {}
            for cookie in response.cookies.jar:
                cookies[cookie.name] = cookie.value
            
            # 处理重定向
            if response.status_code in (301, 302, 303, 307, 308) and 'location' in response.headers:
                next_url = response.headers['location']
                next_response = await self.client.get(next_url, headers=self.headers)
                
                # 合并Cookie
                for cookie in next_response.cookies.jar:
                    cookies[cookie.name] = cookie.value
            
            return cookies
            
        except Exception as e:
            print(f"获取Cookie失败: {str(e)}")
            return {}
    
    async def _get_user_info(self, cookies: Dict[str, str]) -> Dict[str, Any]:
        """获取用户信息"""
        try:
            # 构建Cookie字符串
            cookie_str = '; '.join([f"{k}={v}" for k, v in cookies.items()])
            headers = self.headers.copy()
            headers['Cookie'] = cookie_str
            
            # 请求用户信息API
            user_info_url = "https://www.douyin.com/aweme/v1/web/user/profile/self/"
            params = {
                'device_platform': 'webapp',
                'aid': '6383',
                'channel': 'channel_pc_web',
                'version_code': '170400',
                'version_name': '17.4.0'
            }
            
            response = await self.client.get(user_info_url, params=params, headers=headers)
            response.raise_for_status()
            data = response.json()
            
            if data.get('status_code') == 0 and 'user' in data:
                user_data = data['user']
                return {
                    'uid': user_data.get('uid'),
                    'sec_uid': user_data.get('sec_uid'),
                    'nickname': user_data.get('nickname'),
                    'avatar': user_data.get('avatar_larger', {}).get('url_list', [None])[0],
                    'signature': user_data.get('signature'),
                    'verified': user_data.get('is_verified'),
                    'follower_count': user_data.get('follower_count'),
                    'following_count': user_data.get('following_count'),
                    'favoriting_count': user_data.get('favoriting_count'),
                    'aweme_count': user_data.get('aweme_count')
                }
            else:
                return {}
                
        except Exception as e:
            print(f"获取用户信息失败: {str(e)}")
            return {}


# 创建服务实例
douyin_qrlogin_service = DouyinQRLoginService() 