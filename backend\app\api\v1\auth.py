from fastapi import APIRouter, Depends, HTTPException, status, Request
from fastapi.security import OAuth2PasswordRequestForm
from sqlalchemy.orm import Session
from datetime import timedel<PERSON>, datetime
import json

from ...core.database import get_db
from ...core.security import (
    verify_password, 
    get_password_hash, 
    create_access_token,
    create_refresh_token,
    verify_refresh_token
)
from ...core.config import settings
from ...models.user import User
from ...schemas.user import (
    User<PERSON><PERSON>, 
    UserRegister, 
    User as UserSchema, 
    Token, 
    UserLogin,
    PasswordChange,
    DouyinAuth,
    DouyinQRCode,
    DouyinQRStatus
)
from ...schemas.response import BaseResponse, ErrorResponse
from ..deps import get_current_user
from ...services.douyin_oauth import douyin_oauth_service
from ...services.douyin_qrlogin import douyin_qrlogin_service

router = APIRouter()


@router.post("/register", response_model=BaseResponse[UserSchema])
async def register(user_data: UserRegister, db: Session = Depends(get_db)):
    """用户注册"""
    # 检查用户名是否已存在
    if db.query(User).filter(User.username == user_data.username).first():
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="用户名已存在"
        )
    
    # 检查邮箱是否已存在
    if user_data.email and db.query(User).filter(User.email == user_data.email).first():
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="邮箱已被注册"
        )
    
    # 创建新用户
    hashed_password = get_password_hash(user_data.password)
    db_user = User(
        username=user_data.username,
        email=user_data.email,
        full_name=user_data.full_name,
        avatar_url=user_data.avatar_url,
        douyin_nickname=user_data.douyin_nickname,
        hashed_password=hashed_password,
        is_active=True,
        is_verified=False,
        is_premium=False
    )
    
    db.add(db_user)
    db.commit()
    db.refresh(db_user)
    
    return BaseResponse(
        message="注册成功",
        data=UserSchema.from_orm(db_user)
    )


@router.post("/login", response_model=BaseResponse[Token])
async def login(user_data: UserLogin, db: Session = Depends(get_db)):
    """用户登录"""
    # 验证用户
    user = db.query(User).filter(User.username == user_data.username).first()
    if not user or not verify_password(user_data.password, user.hashed_password):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="用户名或密码错误",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    if not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="用户账户已被禁用"
        )
    
    # 创建访问令牌
    access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        data={"sub": user.username, "user_id": user.id},
        expires_delta=access_token_expires
    )
    
    # 创建刷新令牌
    refresh_token = create_refresh_token(
        data={"sub": user.username, "user_id": user.id}
    )
    
    # 更新最后登录时间
    user.last_login = datetime.utcnow()
    db.commit()
    
    return BaseResponse(
        message="登录成功",
        data=Token(
            access_token=access_token,
            refresh_token=refresh_token,
            token_type="bearer",
            expires_in=settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60
        )
    )


@router.post("/token", response_model=Token)
async def login_for_access_token(
    form_data: OAuth2PasswordRequestForm = Depends(),
    db: Session = Depends(get_db)
):
    """OAuth2兼容的登录接口"""
    user = db.query(User).filter(User.username == form_data.username).first()
    if not user or not verify_password(form_data.password, user.hashed_password):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="用户名或密码错误",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        data={"sub": user.username, "user_id": user.id},
        expires_delta=access_token_expires
    )
    
    refresh_token = create_refresh_token(
        data={"sub": user.username, "user_id": user.id}
    )
    
    return Token(
        access_token=access_token,
        refresh_token=refresh_token,
        token_type="bearer",
        expires_in=settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60
    )


@router.post("/refresh", response_model=BaseResponse[Token])
async def refresh_token(refresh_token: str, db: Session = Depends(get_db)):
    """刷新访问令牌"""
    try:
        payload = verify_refresh_token(refresh_token)
        username: str = payload.get("sub")
        user_id: int = payload.get("user_id")
        
        if username is None or user_id is None:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="无效的刷新令牌"
            )
        
        user = db.query(User).filter(User.id == user_id).first()
        if not user or not user.is_active:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="用户不存在或已被禁用"
            )
        
        # 创建新的访问令牌
        access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
        access_token = create_access_token(
            data={"sub": user.username, "user_id": user.id},
            expires_delta=access_token_expires
        )
        
        # 创建新的刷新令牌
        new_refresh_token = create_refresh_token(
            data={"sub": user.username, "user_id": user.id}
        )
        
        return BaseResponse(
            message="令牌刷新成功",
            data=Token(
                access_token=access_token,
                refresh_token=new_refresh_token,
                token_type="bearer",
                expires_in=settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60
            )
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="无效的刷新令牌"
        )


@router.get("/me", response_model=BaseResponse[UserSchema])
async def get_current_user_info(current_user: User = Depends(get_current_user)):
    """获取当前用户信息"""
    # 构建用户信息，包括抖音信息
    user_data = UserSchema.from_orm(current_user)
    
    # 如果用户有抖音OAuth信息，添加到响应中
    if current_user.douyin_open_id:
        user_data.douyin_info = {
            "open_id": current_user.douyin_open_id,
            "nickname": current_user.douyin_nickname,
            "avatar": current_user.avatar_url,
            "expires_in": current_user.douyin_expires_in,
            "bind_time": current_user.douyin_token_updated_at.isoformat() if current_user.douyin_token_updated_at else None
        }
    
    return BaseResponse(
        message="获取用户信息成功",
        data=user_data
    )


@router.put("/me", response_model=BaseResponse[UserSchema])
async def update_current_user(
    user_update: dict,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """更新当前用户信息"""
    # 更新允许的字段
    allowed_fields = ["email", "full_name", "avatar_url", "douyin_nickname", "settings"]
    
    for field, value in user_update.items():
        if field in allowed_fields and hasattr(current_user, field):
            setattr(current_user, field, value)
    
    db.commit()
    db.refresh(current_user)
    
    return BaseResponse(
        message="用户信息更新成功",
        data=UserSchema.from_orm(current_user)
    )


@router.post("/change-password", response_model=BaseResponse[str])
async def change_password(
    password_data: PasswordChange,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """修改密码"""
    # 验证当前密码
    if not verify_password(password_data.current_password, current_user.hashed_password):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="当前密码错误"
        )
    
    # 更新密码
    current_user.hashed_password = get_password_hash(password_data.new_password)
    db.commit()
    
    return BaseResponse(
        message="密码修改成功",
        data="密码已成功更新"
    )


@router.post("/douyin-auth", response_model=BaseResponse[UserSchema])
async def douyin_auth(
    auth_data: DouyinAuth,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """抖音账户认证"""
    # 更新抖音相关信息
    if auth_data.douyin_uid:
        current_user.douyin_uid = auth_data.douyin_uid
    if auth_data.douyin_sec_uid:
        current_user.douyin_sec_uid = auth_data.douyin_sec_uid
    if auth_data.douyin_cookie:
        current_user.douyin_cookie = auth_data.douyin_cookie
    if auth_data.douyin_nickname:
        current_user.douyin_nickname = auth_data.douyin_nickname
    
    # 更新OAuth相关信息
    if auth_data.access_token:
        current_user.douyin_access_token = auth_data.access_token
        current_user.douyin_refresh_token = auth_data.refresh_token
        current_user.douyin_expires_in = auth_data.expires_in
        current_user.douyin_open_id = auth_data.douyin_open_id
        current_user.douyin_token_updated_at = datetime.utcnow()
    
    db.commit()
    db.refresh(current_user)
    
    # 构建响应数据
    user_data = UserSchema.from_orm(current_user)
    
    # 添加抖音信息
    if current_user.douyin_open_id:
        user_data.douyin_info = {
            "open_id": current_user.douyin_open_id,
            "nickname": current_user.douyin_nickname,
            "avatar": current_user.avatar_url,
            "expires_in": current_user.douyin_expires_in,
            "bind_time": current_user.douyin_token_updated_at.isoformat() if current_user.douyin_token_updated_at else None
        }
    
    return BaseResponse(
        message="抖音账户认证成功",
        data=user_data
    )


@router.get("/douyin-qrcode", response_model=BaseResponse[DouyinQRCode])
async def get_douyin_qrcode():
    """获取抖音登录二维码"""
    try:
        # 使用新的扫码登录服务
        qr_data = await douyin_qrlogin_service.generate_qrcode()
        return BaseResponse(
            message="获取二维码成功",
            data=DouyinQRCode(
                qr_code=qr_data["qr_code"],
                token=qr_data["token"],
                expires_in=qr_data["expires_in"]
            )
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"生成二维码失败: {str(e)}"
        )


@router.get("/douyin-qrcode/check", response_model=BaseResponse[DouyinQRStatus])
async def check_douyin_qrcode_status(token: str, db: Session = Depends(get_db)):
    """检查抖音登录二维码状态"""
    try:
        # 使用新的扫码登录服务检查状态
        status_data = await douyin_qrlogin_service.check_qrcode_status(token)
        
        # 如果状态是已确认，并且有用户信息，创建或更新用户
        if status_data.get("status") == "confirmed" and status_data.get("user_info"):
            user_info = status_data["user_info"]["user_info"]
            cookies = status_data["user_info"]["cookies"]
            
            # 将cookie转换为字符串
            cookie_str = '; '.join([f"{k}={v}" for k, v in cookies.items()])
            
            # 查找或创建用户
            uid = user_info.get('uid')
            sec_uid = user_info.get('sec_uid')
            
            # 先尝试通过uid查找用户
            user = None
            if uid:
                user = db.query(User).filter(User.douyin_uid == uid).first()
            
            # 如果没找到，再尝试通过sec_uid查找
            if not user and sec_uid:
                user = db.query(User).filter(User.douyin_sec_uid == sec_uid).first()
            
            # 如果仍然没找到，创建新用户
            if not user:
                # 创建新用户
                username = f"douyin_{uid or sec_uid}"
                # 确保用户名唯一
                count = 0
                while db.query(User).filter(User.username == username).first():
                    count += 1
                    username = f"douyin_{uid or sec_uid}_{count}"
                
                # 生成随机密码
                import secrets
                import string
                password = ''.join(secrets.choice(string.ascii_letters + string.digits) for _ in range(16))
                hashed_password = get_password_hash(password)
                
                user = User(
                    username=username,
                    hashed_password=hashed_password,
                    douyin_uid=uid,
                    douyin_sec_uid=sec_uid,
                    douyin_nickname=user_info.get('nickname'),
                    avatar_url=user_info.get('avatar'),
                    douyin_cookie=cookie_str,
                    is_active=True
                )
                
                db.add(user)
                db.commit()
                db.refresh(user)
            else:
                # 更新现有用户
                user.douyin_uid = uid or user.douyin_uid
                user.douyin_sec_uid = sec_uid or user.douyin_sec_uid
                user.douyin_nickname = user_info.get('nickname') or user.douyin_nickname
                user.avatar_url = user_info.get('avatar') or user.avatar_url
                user.douyin_cookie = cookie_str
                user.last_login = datetime.utcnow()
                
                db.commit()
            
            # 创建访问令牌
            access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
            access_token = create_access_token(
                data={"sub": user.username, "user_id": user.id},
                expires_delta=access_token_expires
            )
            
            # 创建刷新令牌
            refresh_token = create_refresh_token(
                data={"sub": user.username, "user_id": user.id}
            )
            
            # 添加登录信息到返回数据
            status_data["user_info"]["token"] = {
                "access_token": access_token,
                "refresh_token": refresh_token,
                "token_type": "bearer",
                "expires_in": settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60
            }
        
        return BaseResponse(
            message="获取二维码状态成功",
            data=DouyinQRStatus(
                status=status_data["status"],
                user_info=status_data.get("user_info")
            )
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"检查二维码状态失败: {str(e)}"
        )


@router.get("/douyin-callback")
async def douyin_oauth_callback(code: str, state: str, db: Session = Depends(get_db)):
    """抖音OAuth回调处理"""
    try:
        # 处理OAuth回调
        oauth_result = await douyin_oauth_service.handle_oauth_callback(code, state)
        
        # 从结果中获取用户信息
        open_id = oauth_result["open_id"]
        access_token = oauth_result["access_token"]
        refresh_token = oauth_result["refresh_token"]
        expires_in = oauth_result["expires_in"]
        user_info = oauth_result["user_info"]
        
        # 查找或创建用户
        user = db.query(User).filter(User.douyin_open_id == open_id).first()
        
        if not user:
            # 创建新用户
            username = f"douyin_{open_id}"
            # 确保用户名唯一
            count = 0
            while db.query(User).filter(User.username == username).first():
                count += 1
                username = f"douyin_{open_id}_{count}"
            
            # 生成随机密码
            import secrets
            import string
            password = ''.join(secrets.choice(string.ascii_letters + string.digits) for _ in range(16))
            hashed_password = get_password_hash(password)
            
            user = User(
                username=username,
                hashed_password=hashed_password,
                douyin_open_id=open_id,
                douyin_nickname=user_info.get("nickname"),
                avatar_url=user_info.get("avatar"),
                douyin_access_token=access_token,
                douyin_refresh_token=refresh_token,
                douyin_expires_in=expires_in,
                douyin_token_updated_at=datetime.utcnow(),
                is_active=True
            )
            
            db.add(user)
            db.commit()
            db.refresh(user)
        else:
            # 更新现有用户
            user.douyin_access_token = access_token
            user.douyin_refresh_token = refresh_token
            user.douyin_expires_in = expires_in
            user.douyin_token_updated_at = datetime.utcnow()
            user.douyin_nickname = user_info.get("nickname", user.douyin_nickname)
            user.avatar_url = user_info.get("avatar", user.avatar_url)
            user.last_login = datetime.utcnow()
            
            db.commit()
        
        # 返回HTML页面，关闭窗口并通知父窗口认证成功
        html_content = """
        <!DOCTYPE html>
        <html>
        <head>
            <title>抖音授权成功</title>
            <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <style>
                body {
                    font-family: Arial, sans-serif;
                    text-align: center;
                    padding: 50px;
                    background-color: #f8f8f8;
                }
                .success-box {
                    max-width: 500px;
                    margin: 0 auto;
                    background-color: white;
                    padding: 30px;
                    border-radius: 10px;
                    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                }
                .icon {
                    font-size: 60px;
                    color: #52c41a;
                    margin-bottom: 20px;
                }
                h1 {
                    color: #333;
                    margin-bottom: 20px;
                }
                p {
                    color: #666;
                    margin-bottom: 30px;
                }
            </style>
        </head>
        <body>
            <div class="success-box">
                <div class="icon">✓</div>
                <h1>授权成功</h1>
                <p>抖音账号授权成功，请关闭此窗口返回应用。</p>
            </div>
            <script>
                // 通知父窗口认证成功
                if (window.opener) {
                    window.opener.postMessage({ type: 'douyin-auth-success' }, '*');
                    setTimeout(function() {
                        window.close();
                    }, 3000);
                }
            </script>
        </body>
        </html>
        """
        
        from fastapi.responses import HTMLResponse
        return HTMLResponse(content=html_content)
        
    except Exception as e:
        # 返回错误HTML页面
        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>抖音授权失败</title>
            <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <style>
                body {{
                    font-family: Arial, sans-serif;
                    text-align: center;
                    padding: 50px;
                    background-color: #f8f8f8;
                }}
                .error-box {{
                    max-width: 500px;
                    margin: 0 auto;
                    background-color: white;
                    padding: 30px;
                    border-radius: 10px;
                    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                }}
                .icon {{
                    font-size: 60px;
                    color: #ff4d4f;
                    margin-bottom: 20px;
                }}
                h1 {{
                    color: #333;
                    margin-bottom: 20px;
                }}
                p {{
                    color: #666;
                    margin-bottom: 30px;
                }}
            </style>
        </head>
        <body>
            <div class="error-box">
                <div class="icon">✗</div>
                <h1>授权失败</h1>
                <p>抖音账号授权失败，请关闭此窗口重试。</p>
                <p>错误信息: {str(e)}</p>
            </div>
            <script>
                if (window.opener) {{
                    window.opener.postMessage({{ type: 'douyin-auth-error', error: '{str(e)}' }}, '*');
                    setTimeout(function() {{
                        window.close();
                    }}, 5000);
                }}
            </script>
        </body>
        </html>
        """
        
        from fastapi.responses import HTMLResponse
        return HTMLResponse(content=html_content, status_code=400)


@router.post("/logout", response_model=BaseResponse[str])
async def logout(current_user: User = Depends(get_current_user)):
    """用户登出"""
    # 在实际应用中，这里应该将令牌加入黑名单
    # 或者在数据库中标记会话为无效
    return BaseResponse(
        message="登出成功",
        data="用户已成功登出"
    )
