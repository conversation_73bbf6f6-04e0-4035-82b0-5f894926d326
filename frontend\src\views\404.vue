<template>
  <div class="error-container">
    <div class="error-content">
      <!-- 404 动画图标 -->
      <div class="error-animation">
        <div class="error-number">
          <span class="four">4</span>
          <span class="zero">0</span>
          <span class="four">4</span>
        </div>
        <div class="error-icon">
          <div class="sad-face">
            <div class="eyes">
              <div class="eye left"></div>
              <div class="eye right"></div>
            </div>
            <div class="mouth"></div>
          </div>
        </div>
      </div>

      <!-- 错误信息 -->
      <div class="error-info">
        <h1>页面未找到</h1>
        <p class="error-message">
          抱歉，您访问的页面不存在或已被移动。
        </p>
        <p class="error-suggestion">
          可能的原因：
        </p>
        <ul class="error-reasons">
          <li>页面地址输入错误</li>
          <li>页面已被删除或移动</li>
          <li>您没有访问权限</li>
          <li>服务器暂时无法响应</li>
        </ul>
      </div>

      <!-- 操作按钮 -->
      <div class="error-actions">
        <el-button @click="goHome" type="primary" size="large">
          <el-icon><House /></el-icon>
          返回主页
        </el-button>
        <el-button @click="goBack" size="large">
          <el-icon><ArrowLeft /></el-icon>
          返回上页
        </el-button>
        <el-button @click="refresh" size="large">
          <el-icon><Refresh /></el-icon>
          刷新页面
        </el-button>
      </div>

      <!-- 搜索建议 -->
      <div class="search-suggestion">
        <p>或者尝试搜索您需要的内容：</p>
        <div class="search-box">
          <el-input
            v-model="searchKeyword"
            placeholder="搜索页面或功能..."
            size="large"
            @keyup.enter="handleSearch"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
            <template #append>
              <el-button @click="handleSearch" type="primary">
                搜索
              </el-button>
            </template>
          </el-input>
        </div>
      </div>

      <!-- 快速导航 -->
      <div class="quick-nav">
        <h3>快速导航</h3>
        <div class="nav-grid">
          <div class="nav-item" @click="navigateTo('/history')">
            <div class="nav-icon">
              <el-icon><VideoPlay /></el-icon>
            </div>
            <div class="nav-text">
              <div class="nav-title">观看历史</div>
              <div class="nav-desc">查看您的观看记录</div>
            </div>
          </div>
          
          <div class="nav-item" @click="navigateTo('/analysis')">
            <div class="nav-icon">
              <el-icon><TrendCharts /></el-icon>
            </div>
            <div class="nav-text">
              <div class="nav-title">数据分析</div>
              <div class="nav-desc">分析观看习惯</div>
            </div>
          </div>
          
          <div class="nav-item" @click="navigateTo('/download')">
            <div class="nav-icon">
              <el-icon><Download /></el-icon>
            </div>
            <div class="nav-text">
              <div class="nav-title">下载管理</div>
              <div class="nav-desc">管理下载任务</div>
            </div>
          </div>
          
          <div class="nav-item" @click="navigateTo('/settings')">
            <div class="nav-icon">
              <el-icon><Setting /></el-icon>
            </div>
            <div class="nav-text">
              <div class="nav-title">系统设置</div>
              <div class="nav-desc">配置应用选项</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 联系支持 -->
      <div class="support-section">
        <p>如果问题持续存在，请联系技术支持：</p>
        <div class="support-links">
          <el-button type="primary" link>
            <el-icon><Message /></el-icon>
            在线客服
          </el-button>
          <el-button type="primary" link>
            <el-icon><Phone /></el-icon>
            技术支持
          </el-button>
          <el-button type="primary" link>
            <el-icon><QuestionFilled /></el-icon>
            帮助中心
          </el-button>
        </div>
      </div>
    </div>

    <!-- 背景装饰 -->
    <div class="background-decoration">
      <div class="floating-shape shape-1"></div>
      <div class="floating-shape shape-2"></div>
      <div class="floating-shape shape-3"></div>
      <div class="floating-shape shape-4"></div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'

const router = useRouter()
const searchKeyword = ref('')

// 方法
const goHome = () => {
  router.push('/')
}

const goBack = () => {
  if (window.history.length > 1) {
    router.go(-1)
  } else {
    router.push('/')
  }
}

const refresh = () => {
  window.location.reload()
}

const handleSearch = () => {
  if (!searchKeyword.value.trim()) {
    ElMessage.warning('请输入搜索关键词')
    return
  }
  
  // 这里可以实现搜索功能
  ElMessage.info('搜索功能开发中...')
}

const navigateTo = (path) => {
  router.push(path)
}
</script>

<style lang="scss" scoped>
.error-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, var(--primary-color) 0%, #ff5e78 100%);
  position: relative;
  overflow: hidden;
  padding: 20px;
}

.error-content {
  max-width: 800px;
  width: 100%;
  text-align: center;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: var(--border-radius-large);
  padding: 60px 40px;
  box-shadow: var(--shadow-light);
  position: relative;
  z-index: 10;
}

.error-animation {
  margin-bottom: 40px;
  
  .error-number {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 20px;
    margin-bottom: 30px;
    
    span {
      font-size: 120px;
      font-weight: 800;
      background: linear-gradient(45deg, var(--primary-color), #FF6B6B);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      animation: bounce 2s infinite;
      
      &.zero {
        animation-delay: 0.2s;
      }
      
      &.four:last-child {
        animation-delay: 0.4s;
      }
    }
  }
  
  .error-icon {
    .sad-face {
      width: 80px;
      height: 80px;
      margin: 0 auto;
      position: relative;
      
      .eyes {
        display: flex;
        justify-content: space-between;
        padding: 20px 15px;
        
        .eye {
          width: 12px;
          height: 12px;
          background-color: var(--primary-color);
          border-radius: 50%;
          animation: blink 3s infinite;
        }
      }
      
      .mouth {
        width: 40px;
        height: 20px;
        border-bottom-left-radius: 40px;
        border-bottom-right-radius: 40px;
        border: 3px solid var(--primary-color);
        border-top: none;
        margin: 0 auto;
      }
    }
  }
}

.error-info {
  margin-bottom: 40px;
  
  h1 {
    font-size: 32px;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0 0 16px 0;
  }
  
  .error-message {
    font-size: 18px;
    color: var(--text-secondary);
    margin: 0 0 24px 0;
  }
  
  .error-suggestion {
    font-size: 16px;
    font-weight: 500;
    color: var(--text-primary);
    margin: 0 0 8px 0;
  }
  
  .error-reasons {
    list-style-type: none;
    padding: 0;
    margin: 0;
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 16px;
    
    li {
      padding: 8px 16px;
      background-color: rgba(254, 44, 85, 0.1);
      color: var(--primary-color);
      border-radius: 20px;
      font-size: 14px;
    }
  }
}

.error-actions {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  gap: 16px;
  margin-bottom: 40px;
  
  .el-button {
    &.el-button--primary {
      background-color: var(--primary-color);
      border-color: var(--primary-color);
      
      &:hover, &:focus {
        background-color: var(--primary-light);
        border-color: var(--primary-light);
      }
    }
  }
}

.search-suggestion {
  margin-bottom: 40px;
  
  p {
    font-size: 16px;
    color: var(--text-secondary);
    margin: 0 0 16px 0;
  }
  
  .search-box {
    max-width: 500px;
    margin: 0 auto;
    
    :deep(.el-input__wrapper) {
      border-radius: var(--border-radius-base);
    }
    
    :deep(.el-input-group__append) {
      .el-button {
        background-color: var(--primary-color);
        border-color: var(--primary-color);
        color: white;
        
        &:hover, &:focus {
          background-color: var(--primary-light);
          border-color: var(--primary-light);
        }
      }
    }
  }
}

.quick-nav {
  margin-bottom: 40px;
  
  h3 {
    font-size: 18px;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0 0 20px 0;
  }
  
  .nav-grid {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 16px;
    
    .nav-item {
      display: flex;
      align-items: center;
      gap: 12px;
      width: 220px;
      background-color: white;
      border-radius: var(--border-radius-base);
      padding: 16px;
      box-shadow: var(--shadow-light);
      cursor: pointer;
      transition: transform 0.2s;
      
      &:hover {
        transform: translateY(-5px);
      }
      
      .nav-icon {
        width: 40px;
        height: 40px;
        border-radius: 12px;
        background-color: rgba(254, 44, 85, 0.1);
        color: var(--primary-color);
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 20px;
        flex-shrink: 0;
      }
      
      .nav-text {
        flex: 1;
        
        .nav-title {
          font-size: 14px;
          font-weight: 500;
          color: var(--text-primary);
          margin: 0 0 4px 0;
        }
        
        .nav-desc {
          font-size: 12px;
          color: var(--text-secondary);
          margin: 0;
        }
      }
    }
  }
}

.support-section {
  p {
    font-size: 14px;
    color: var(--text-secondary);
    margin: 0 0 16px 0;
  }
  
  .support-links {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: 16px;
    
    .el-button {
      color: var(--primary-color);
      
      &:hover, &:focus {
        color: var(--primary-light);
      }
    }
  }
}

.background-decoration {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  pointer-events: none;
  
  .floating-shape {
    position: absolute;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    
    &.shape-1 {
      width: 300px;
      height: 300px;
      top: -100px;
      left: -100px;
      animation: float 15s infinite linear;
    }
    
    &.shape-2 {
      width: 200px;
      height: 200px;
      top: 10%;
      right: -50px;
      animation: float 12s infinite linear reverse;
    }
    
    &.shape-3 {
      width: 150px;
      height: 150px;
      bottom: 10%;
      left: 5%;
      animation: float 10s infinite linear;
    }
    
    &.shape-4 {
      width: 250px;
      height: 250px;
      bottom: -50px;
      right: 10%;
      animation: float 18s infinite linear reverse;
    }
  }
}

@keyframes bounce {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-20px);
  }
}

@keyframes blink {
  0%, 100% {
    transform: scale(1);
  }
  10% {
    transform: scale(1, 0.1);
  }
  20% {
    transform: scale(1);
  }
}

@keyframes float {
  0% {
    transform: rotate(0deg) translate(20px) rotate(0deg);
  }
  100% {
    transform: rotate(360deg) translate(20px) rotate(-360deg);
  }
}

// 响应式
@media (max-width: 768px) {
  .error-content {
    padding: 40px 20px;
  }
  
  .error-number {
    span {
      font-size: 80px !important;
    }
  }
  
  .error-info {
    h1 {
      font-size: 24px;
    }
    
    .error-message {
      font-size: 16px;
    }
  }
  
  .nav-grid {
    .nav-item {
      width: 100%;
    }
  }
}

// 暗色主题适配
.dark {
  .error-content {
    background: rgba(30, 30, 30, 0.95);
  }
  
  .nav-item {
    background-color: #2a2a2a;
  }
}
</style>
