<template>
  <div class="login-container">
    <div class="login-box">
      <div class="login-header">
        <img src="/logo.svg" alt="抖音历史记录分析工具" class="logo" />
        <h1 class="title">抖音历史记录分析工具</h1>
        <p class="subtitle">使用抖音账号登录</p>
      </div>

      <div class="login-content">
        <div class="qr-section">
          <div v-if="qrCode" class="qr-code">
            <img :src="qrCode" alt="抖音登录二维码" />
            <div class="qr-status">
              <el-icon v-if="qrStatus === 'loading' || qrStatus === 'scanned'" class="rotating"><Loading /></el-icon>
              <el-icon v-else-if="qrStatus === 'confirmed'"><SuccessFilled /></el-icon>
              <el-icon v-else-if="qrStatus === 'expired' || qrStatus === 'error'"><WarningFilled /></el-icon>
              <span>{{ qrStatusText }}</span>
            </div>
          </div>
          <div v-else class="qr-placeholder">
            <el-icon><QrCode /></el-icon>
            <p>点击下方按钮生成登录二维码</p>
          </div>

          <div class="qr-actions">
            <el-button @click="generateQRCode" :loading="generatingQR" type="primary" size="large">
              <el-icon><Refresh /></el-icon>
              {{ qrCode ? '刷新二维码' : '生成二维码' }}
            </el-button>
          </div>
        </div>

        <div class="login-instructions">
          <h3>登录说明</h3>
          <ol>
            <li>点击"生成二维码"按钮</li>
            <li>打开<strong>抖音 App</strong></li>
            <li>使用抖音扫一扫功能扫描二维码</li>
            <li>在手机上确认授权登录</li>
          </ol>
          <div class="login-tips">
            <el-icon><InfoFilled /></el-icon>
            <span>登录后即可同步您的抖音历史记录，分析您的观看习惯</span>
          </div>
        </div>
      </div>

      <div class="login-footer">
        <p>登录即表示您同意我们的<a href="#">服务条款</a>和<a href="#">隐私政策</a></p>
      </div>
    </div>

    <!-- 背景装饰 -->
    <div class="bg-decoration">
      <div class="circle circle-1"></div>
      <div class="circle circle-2"></div>
      <div class="circle circle-3"></div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { authAPI } from '@/utils/api'
import { ElMessage } from 'element-plus'

const router = useRouter()
const route = useRoute()
const authStore = useAuthStore()

// 响应式数据
const generatingQR = ref(false)
const qrCode = ref('')
const qrStatus = ref('waiting')
const qrToken = ref('')
const qrTimer = ref(null)

// 计算属性
const qrStatusText = computed(() => {
  switch (qrStatus.value) {
    case 'waiting':
      return '请使用抖音APP扫描二维码'
    case 'scanned':
      return '已扫描，请在手机上确认登录'
    case 'confirmed':
      return '登录成功，正在跳转...'
    case 'expired':
      return '二维码已过期，请刷新'
    case 'error':
      return '登录失败，请重试'
    default:
      return '加载中...'
  }
})

// 生成二维码
const generateQRCode = async () => {
  try {
    generatingQR.value = true
    const response = await authAPI.getQRCode()

    if (response.success) {
      qrCode.value = response.data.qr_code
      qrToken.value = response.data.token
      qrStatus.value = 'waiting'

      // 开始轮询二维码状态
      startQRPolling()
    }
  } catch (error) {
    ElMessage.error('生成二维码失败')
  } finally {
    generatingQR.value = false
  }
}

// 开始轮询二维码状态
const startQRPolling = () => {
  if (qrTimer.value) {
    clearInterval(qrTimer.value)
  }

  qrTimer.value = setInterval(async () => {
    try {
      const response = await authAPI.checkQRStatus(qrToken.value)

      if (response.success) {
        const statusData = response.data
        qrStatus.value = statusData.status

        if (statusData.status === 'confirmed' && statusData.user_info) {
          // 登录成功
          await handleLoginSuccess(statusData.user_info)
        } else if (statusData.status === 'expired' || statusData.status === 'error') {
          // 二维码过期或错误
          stopQRPolling()
        }
      }
    } catch (error) {
      console.error('检查二维码状态失败:', error)
    }
  }, 2000)
}

// 停止轮询二维码状态
const stopQRPolling = () => {
  if (qrTimer.value) {
    clearInterval(qrTimer.value)
    qrTimer.value = null
  }
}

// 处理登录成功
const handleLoginSuccess = async (userInfo) => {
  try {
    // 设置认证信息
    await authStore.login({
      access_token: userInfo.token.access_token,
      refresh_token: userInfo.token.refresh_token,
      expires_in: userInfo.token.expires_in
    })
    
    ElMessage.success('登录成功')
    
    // 延迟一下再跳转，让用户看到成功状态
    setTimeout(() => {
      // 登录成功，跳转到目标页面
      const redirect = route.query.redirect || '/'
      router.push(redirect)
    }, 1500)
    
    // 停止轮询
    stopQRPolling()
  } catch (error) {
    ElMessage.error('登录处理失败')
    qrStatus.value = 'error'
  }
}

// 组件挂载时自动生成二维码
onMounted(() => {
  generateQRCode()
})

// 组件卸载时清理定时器
onUnmounted(() => {
  stopQRPolling()
})
</script>

<style lang="scss" scoped>
.login-container {
  min-height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: var(--bg-light);
  position: relative;
  overflow: hidden;
  padding: 20px;
}

.login-box {
  width: 100%;
  max-width: 900px;
  background-color: var(--bg-white);
  border-radius: var(--border-radius-large);
  box-shadow: var(--shadow-medium);
  padding: 40px;
  position: relative;
  z-index: 10;
  
  @media (max-width: 768px) {
    padding: 30px 20px;
  }
}

.login-header {
  text-align: center;
  margin-bottom: 40px;
  
  .logo {
    width: 80px;
    height: 80px;
    margin-bottom: 16px;
  }
  
  .title {
    font-size: 28px;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0 0 8px 0;
  }
  
  .subtitle {
    font-size: 16px;
    color: var(--text-secondary);
    margin: 0;
  }
}

.login-content {
  display: flex;
  flex-wrap: wrap;
  gap: 40px;
  margin-bottom: 40px;
  
  @media (max-width: 768px) {
    flex-direction: column;
  }
  
  .qr-section {
    flex: 1;
    min-width: 280px;
    display: flex;
    flex-direction: column;
    align-items: center;
    
    .qr-code, .qr-placeholder {
      width: 280px;
      height: 280px;
      background-color: white;
      border-radius: var(--border-radius-base);
      padding: 16px;
      box-shadow: var(--shadow-light);
      margin-bottom: 24px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      
      img {
        width: 100%;
        height: 100%;
        object-fit: contain;
      }
      
      .qr-status {
        margin-top: 16px;
        text-align: center;
        font-size: 14px;
        color: var(--text-secondary);
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8px;
      }
    }
    
    .qr-placeholder {
      border: 2px dashed var(--border-color);
      color: var(--text-secondary);
      
      .el-icon {
        font-size: 64px;
        margin-bottom: 16px;
        opacity: 0.5;
      }
      
      p {
        margin: 0;
        text-align: center;
      }
    }
    
    .qr-actions {
      width: 100%;
      display: flex;
      justify-content: center;
      
      .el-button {
        width: 100%;
        max-width: 280px;
        height: 48px;
        background-color: var(--primary-color);
        border-color: var(--primary-color);
        
        &:hover, &:focus {
          background-color: var(--primary-light);
          border-color: var(--primary-light);
        }
      }
    }
  }
  
  .login-instructions {
    flex: 1;
    min-width: 280px;
    
    h3 {
      font-size: 18px;
      font-weight: 600;
      color: var(--text-primary);
      margin: 0 0 16px 0;
      position: relative;
      padding-left: 16px;
      
      &:before {
        content: "";
        position: absolute;
        left: 0;
        top: 4px;
        bottom: 4px;
        width: 4px;
        background-color: var(--primary-color);
        border-radius: 2px;
      }
    }
    
    ol {
      margin: 0 0 24px 0;
      padding-left: 20px;
      
      li {
        margin-bottom: 12px;
        color: var(--text-regular);
        font-size: 15px;
        line-height: 1.5;
        
        &:last-child {
          margin-bottom: 0;
        }
        
        strong {
          color: var(--primary-color);
        }
      }
    }
    
    .login-tips {
      background-color: rgba(254, 44, 85, 0.05);
      border-left: 4px solid var(--primary-color);
      padding: 16px;
      border-radius: var(--border-radius-base);
      display: flex;
      align-items: flex-start;
      gap: 12px;
      
      .el-icon {
        color: var(--primary-color);
        font-size: 20px;
        margin-top: 2px;
      }
      
      span {
        flex: 1;
        font-size: 14px;
        color: var(--text-regular);
        line-height: 1.5;
      }
    }
  }
}

.login-footer {
  text-align: center;
  font-size: 14px;
  color: var(--text-secondary);
  
  a {
    color: var(--primary-color);
    text-decoration: none;
    
    &:hover {
      text-decoration: underline;
    }
  }
}

.bg-decoration {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  pointer-events: none;
  
  .circle {
    position: absolute;
    border-radius: 50%;
    
    &.circle-1 {
      width: 300px;
      height: 300px;
      background: linear-gradient(135deg, rgba(254, 44, 85, 0.1), rgba(254, 44, 85, 0.2));
      top: -100px;
      right: 10%;
    }
    
    &.circle-2 {
      width: 500px;
      height: 500px;
      background: linear-gradient(135deg, rgba(254, 44, 85, 0.05), rgba(254, 44, 85, 0.1));
      bottom: -200px;
      left: -100px;
    }
    
    &.circle-3 {
      width: 200px;
      height: 200px;
      background: linear-gradient(135deg, rgba(254, 44, 85, 0.1), rgba(254, 44, 85, 0.15));
      top: 40%;
      left: 10%;
    }
  }
}

// 旋转动画
@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.rotating {
  animation: rotate 1.5s linear infinite;
}

// 暗色主题适配
.dark {
  .login-box {
    background-color: var(--bg-dark);
  }
  
  .qr-code, .qr-placeholder {
    background-color: var(--bg-darker) !important;
  }
}
</style>
