#!/usr/bin/env python3
"""
抖音历史记录分析工具 - 简化版服务器
不依赖任何外部包，使用Python标准库
"""

import json
import sqlite3
from http.server import HTTPServer, BaseHTTPRequestHandler
from urllib.parse import urlparse, parse_qs
import hashlib
import secrets
from datetime import datetime, timedelta
import os
from pathlib import Path

class DouyinAPI(BaseHTTPRequestHandler):
    def do_GET(self):
        path = urlparse(self.path).path
        query = parse_qs(urlparse(self.path).query)

        # 设置CORS头
        self.send_cors_headers()

        if path == '/':
            self.serve_homepage()
        elif path == '/health':
            self.send_json_response({
                "success": True,
                "message": "服务正常运行",
                "version": "1.0.0-simple",
                "timestamp": datetime.now().isoformat()
            })
        elif path == '/api/v1/test':
            self.send_json_response({
                "success": True,
                "message": "API测试成功",
                "data": {
                    "server": "Python标准库HTTP服务器",
                    "features": [
                        "基础HTTP服务",
                        "JSON API响应",
                        "SQLite数据库支持",
                        "CORS跨域支持"
                    ]
                }
            })
        elif path == '/api/v1/status':
            self.get_system_status()
        elif path == '/api/v1/auth/douyin-qrcode':
            self.get_douyin_qrcode()
        elif path.startswith('/api/v1/auth/douyin-qrcode/check'):
            self.check_qrcode_status(query)
        else:
            self.send_error_response(404, "接口不存在")

    def do_POST(self):
        # 处理POST请求
        self.send_cors_headers()

        path = urlparse(self.path).path

        if path == '/api/v1/echo':
            # 回显测试
            content_length = int(self.headers.get('Content-Length', 0))
            post_data = self.rfile.read(content_length)

            try:
                data = json.loads(post_data.decode('utf-8'))
                self.send_json_response({
                    "success": True,
                    "message": "数据接收成功",
                    "received_data": data
                })
            except json.JSONDecodeError:
                self.send_error_response(400, "JSON格式错误")
        else:
            self.send_error_response(404, "接口不存在")

    def do_OPTIONS(self):
        # 处理预检请求
        self.send_cors_headers()
        self.send_response(200)
        self.end_headers()

    def send_cors_headers(self):
        """发送CORS头"""
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type, Authorization')

    def serve_homepage(self):
        """提供主页"""
        html = '''
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>抖音历史记录分析工具 - 简化版</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        h1 { color: #333; text-align: center; }
        .status { background: #e8f5e8; padding: 15px; border-radius: 5px; margin: 20px 0; }
        .api-list { background: #f8f9fa; padding: 20px; border-radius: 5px; }
        .api-item { margin: 10px 0; }
        .api-item a { color: #007bff; text-decoration: none; }
        .api-item a:hover { text-decoration: underline; }
        .warning { background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 20px 0; }
        .btn { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; margin: 5px; }
        .btn:hover { background: #0056b3; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎵 抖音历史记录分析工具</h1>

        <div class="status">
            <h3>✅ 服务状态</h3>
            <p>简化版后端服务正在运行！</p>
            <p>服务器时间: <span id="time"></span></p>
        </div>

        <div class="warning">
            <h3>⚠️ 注意</h3>
            <p>这是简化版本，使用Python标准库实现。要获得完整功能，请解决pip依赖问题后安装完整版本。</p>
        </div>

        <div class="api-list">
            <h3>📡 可用API接口</h3>
            <div class="api-item">
                <strong>GET</strong> <a href="/health">/health</a> - 健康检查
            </div>
            <div class="api-item">
                <strong>GET</strong> <a href="/api/v1/test">/api/v1/test</a> - API测试
            </div>
            <div class="api-item">
                <strong>GET</strong> <a href="/api/v1/status">/api/v1/status</a> - 系统状态
            </div>
            <div class="api-item">
                <strong>POST</strong> /api/v1/echo - 数据回显测试
            </div>
            <div class="api-item">
                <strong>GET</strong> <a href="/api/v1/auth/douyin-qrcode">/api/v1/auth/douyin-qrcode</a> - 获取抖音登录二维码
            </div>
            <div class="api-item">
                <strong>GET</strong> /api/v1/auth/douyin-qrcode/check?token=xxx - 检查二维码状态
            </div>
        </div>

        <div style="margin-top: 30px;">
            <h3>🧪 API测试</h3>
            <button class="btn" onclick="testAPI()">测试API连接</button>
            <button class="btn" onclick="testEcho()">测试POST请求</button>
            <div id="result" style="margin-top: 15px; padding: 10px; background: #f8f9fa; border-radius: 5px; display: none;"></div>
        </div>

        <div style="margin-top: 30px; text-align: center; color: #666;">
            <p>抖音历史记录分析工具 v1.0.0 - 简化版</p>
        </div>
    </div>

    <script>
        // 更新时间
        function updateTime() {
            document.getElementById('time').textContent = new Date().toLocaleString('zh-CN');
        }
        updateTime();
        setInterval(updateTime, 1000);

        // 测试API
        async function testAPI() {
            try {
                const response = await fetch('/api/v1/test');
                const data = await response.json();
                showResult('API测试成功', data);
            } catch (error) {
                showResult('API测试失败', {error: error.message});
            }
        }

        // 测试POST
        async function testEcho() {
            try {
                const testData = {message: 'Hello from frontend!', timestamp: new Date().toISOString()};
                const response = await fetch('/api/v1/echo', {
                    method: 'POST',
                    headers: {'Content-Type': 'application/json'},
                    body: JSON.stringify(testData)
                });
                const data = await response.json();
                showResult('POST测试成功', data);
            } catch (error) {
                showResult('POST测试失败', {error: error.message});
            }
        }

        // 显示结果
        function showResult(title, data) {
            const result = document.getElementById('result');
            result.innerHTML = '<h4>' + title + '</h4><pre>' + JSON.stringify(data, null, 2) + '</pre>';
            result.style.display = 'block';
        }
    </script>
</body>
</html>
        '''

        self.send_response(200)
        self.send_header('Content-type', 'text/html; charset=utf-8')
        self.end_headers()
        self.wfile.write(html.encode('utf-8'))

    def get_system_status(self):
        """获取系统状态"""
        import platform

        status = {
            "success": True,
            "data": {
                "system": {
                    "platform": platform.system(),
                    "python_version": platform.python_version(),
                    "architecture": platform.architecture()[0]
                },
                "server": {
                    "type": "Python标准库HTTP服务器",
                    "port": 8000,
                    "start_time": datetime.now().isoformat()
                },
                "directories": {
                    "data": os.path.exists("data"),
                    "downloads": os.path.exists("downloads"),
                    "logs": os.path.exists("logs")
                }
            }
        }

        self.send_json_response(status)

    def get_douyin_qrcode(self):
        """生成抖音登录二维码（模拟）"""
        import base64
        import uuid

        # 生成模拟的二维码数据
        qr_token = str(uuid.uuid4())

        # 创建一个简单的SVG二维码图像（模拟）
        svg_qr = f'''<svg width="200" height="200" xmlns="http://www.w3.org/2000/svg">
            <rect width="200" height="200" fill="white"/>
            <rect x="20" y="20" width="160" height="160" fill="black"/>
            <rect x="40" y="40" width="120" height="120" fill="white"/>
            <text x="100" y="105" text-anchor="middle" font-family="Arial" font-size="12" fill="black">
                抖音登录
            </text>
            <text x="100" y="125" text-anchor="middle" font-family="Arial" font-size="8" fill="black">
                {qr_token[:8]}...
            </text>
        </svg>'''

        # 转换为base64
        qr_base64 = base64.b64encode(svg_qr.encode()).decode()

        response_data = {
            "success": True,
            "message": "获取二维码成功",
            "data": {
                "qr_code": f"data:image/svg+xml;base64,{qr_base64}",
                "token": qr_token,
                "expires_in": 300
            }
        }

        self.send_json_response(response_data)

    def check_qrcode_status(self, query):
        """检查二维码状态（模拟）"""
        token = query.get('token', [''])[0]

        if not token:
            self.send_error_response(400, "缺少token参数")
            return

        # 模拟不同的状态
        import random
        statuses = ['waiting', 'scanned', 'confirmed', 'expired']
        status = random.choice(statuses[:2])  # 主要返回waiting或scanned

        response_data = {
            "success": True,
            "message": "获取二维码状态成功",
            "data": {
                "status": status,
                "user_info": {
                    "nickname": "测试用户",
                    "avatar": "https://example.com/avatar.jpg"
                } if status == "confirmed" else None
            }
        }

        self.send_json_response(response_data)

    def send_json_response(self, data):
        """发送JSON响应"""
        self.send_response(200)
        self.send_header('Content-type', 'application/json; charset=utf-8')
        self.send_cors_headers()
        self.end_headers()
        self.wfile.write(json.dumps(data, ensure_ascii=False, indent=2).encode('utf-8'))

    def send_error_response(self, code, message):
        """发送错误响应"""
        self.send_response(code)
        self.send_header('Content-type', 'application/json; charset=utf-8')
        self.send_cors_headers()
        self.end_headers()
        error_data = {
            "success": False,
            "error": message,
            "code": code,
            "timestamp": datetime.now().isoformat()
        }
        self.wfile.write(json.dumps(error_data, ensure_ascii=False).encode('utf-8'))

def create_directories():
    """创建必要目录"""
    dirs = ["data", "downloads", "logs"]
    for dir_name in dirs:
        Path(dir_name).mkdir(exist_ok=True)
        print(f"✓ 创建目录: {dir_name}")

def main():
    """主函数"""
    print("=" * 60)
    print("🎵 抖音历史记录分析工具 - 简化版服务器")
    print("=" * 60)

    # 创建必要目录
    create_directories()

    # 启动服务器
    server_address = ('localhost', 8000)
    httpd = HTTPServer(server_address, DouyinAPI)

    print(f"\n✅ 服务器启动成功!")
    print(f"📍 访问地址: http://localhost:8000")
    print(f"📡 API文档: http://localhost:8000/api/v1/test")
    print(f"🔍 健康检查: http://localhost:8000/health")
    print(f"\n💡 这是简化版本，使用Python标准库实现")
    print(f"🔧 要获得完整功能，请解决pip依赖问题")
    print(f"\n按 Ctrl+C 停止服务器")
    print("=" * 60)

    try:
        httpd.serve_forever()
    except KeyboardInterrupt:
        print("\n\n🛑 服务器已停止")
        httpd.shutdown()

if __name__ == "__main__":
    main()
