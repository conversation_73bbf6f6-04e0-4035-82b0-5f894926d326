// CSS Reset - 重置默认样式

/* 通用重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

*::before,
*::after {
  box-sizing: border-box;
}

/* HTML 和 Body */
html {
  line-height: 1.15;
  -webkit-text-size-adjust: 100%;
  -webkit-tap-highlight-color: transparent;
}

body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #f5f5f5;
  color: #333;
}

/* 标题 */
h1, h2, h3, h4, h5, h6 {
  margin: 0;
  font-weight: normal;
}

/* 段落 */
p {
  margin: 0;
}

/* 列表 */
ul, ol {
  list-style: none;
  margin: 0;
  padding: 0;
}

/* 链接 */
a {
  color: inherit;
  text-decoration: none;
  background-color: transparent;
}

a:hover {
  text-decoration: none;
}

/* 按钮 */
button {
  margin: 0;
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
  background: transparent;
  border: 0;
  cursor: pointer;
}

button:focus {
  outline: none;
}

/* 输入框 */
input, textarea, select {
  margin: 0;
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
  border: 0;
  outline: none;
}

/* 图片 */
img {
  max-width: 100%;
  height: auto;
  border-style: none;
}

/* 表格 */
table {
  border-collapse: collapse;
  border-spacing: 0;
}

/* 其他元素 */
hr {
  box-sizing: content-box;
  height: 0;
  overflow: visible;
}

code, kbd, pre, samp {
  font-family: monospace, monospace;
  font-size: 1em;
}

small {
  font-size: 80%;
}

sub, sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}

sub {
  bottom: -0.25em;
}

sup {
  top: -0.5em;
}
