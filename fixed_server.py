#!/usr/bin/env python3
"""
修复版服务器 - 解决响应格式问题
"""

import json
from http.server import HTTPServer, BaseHTTPRequestHandler
from urllib.parse import urlparse, parse_qs
from datetime import datetime
import os
from pathlib import Path

class FixedDouyinAPI(BaseHTTPRequestHandler):
    
    def log_message(self, format, *args):
        """重写日志方法，简化输出"""
        print(f"[{datetime.now().strftime('%H:%M:%S')}] {format % args}")
    
    def do_GET(self):
        path = urlparse(self.path).path
        
        try:
            if path == '/':
                self.serve_homepage()
            elif path == '/health':
                self.send_json_response({
                    "success": True,
                    "message": "服务正常运行",
                    "version": "1.0.0-fixed",
                    "timestamp": datetime.now().isoformat()
                })
            elif path == '/api/v1/test':
                self.send_json_response({
                    "success": True,
                    "message": "API测试成功",
                    "data": {
                        "server": "修复版Python服务器",
                        "features": ["HTTP服务", "JSON API", "CORS支持"]
                    }
                })
            elif path == '/api/v1/status':
                self.get_system_status()
            else:
                self.send_error_response(404, "接口不存在")
        except Exception as e:
            print(f"处理请求出错: {e}")
            self.send_error_response(500, "服务器内部错误")
    
    def do_POST(self):
        try:
            path = urlparse(self.path).path
            
            if path == '/api/v1/echo':
                content_length = int(self.headers.get('Content-Length', 0))
                if content_length > 0:
                    post_data = self.rfile.read(content_length)
                    try:
                        data = json.loads(post_data.decode('utf-8'))
                        self.send_json_response({
                            "success": True,
                            "message": "数据接收成功",
                            "received_data": data
                        })
                    except json.JSONDecodeError:
                        self.send_error_response(400, "JSON格式错误")
                else:
                    self.send_error_response(400, "缺少请求数据")
            else:
                self.send_error_response(404, "接口不存在")
        except Exception as e:
            print(f"处理POST请求出错: {e}")
            self.send_error_response(500, "服务器内部错误")
    
    def do_OPTIONS(self):
        """处理预检请求"""
        self.send_response(200)
        self.send_cors_headers()
        self.end_headers()
    
    def send_cors_headers(self):
        """发送CORS头"""
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type, Authorization')
    
    def serve_homepage(self):
        """提供主页"""
        html = '''<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>抖音历史记录分析工具 - 修复版</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; background: #f5f7fa; }
        .container { max-width: 1000px; margin: 0 auto; padding: 20px; }
        .header { text-align: center; margin-bottom: 30px; }
        .title { font-size: 2.5em; color: #2c3e50; margin-bottom: 10px; }
        .subtitle { font-size: 1.2em; color: #7f8c8d; }
        .card { background: white; border-radius: 10px; padding: 25px; margin-bottom: 20px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .status { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; }
        .api-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; }
        .api-item { padding: 15px; background: #f8f9fa; border-radius: 8px; border-left: 4px solid #007bff; }
        .api-method { font-weight: bold; color: #007bff; }
        .api-url { font-family: monospace; background: #e9ecef; padding: 5px 10px; border-radius: 4px; margin: 5px 0; }
        .btn { background: #007bff; color: white; border: none; padding: 12px 24px; border-radius: 6px; cursor: pointer; margin: 5px; font-size: 14px; }
        .btn:hover { background: #0056b3; }
        .btn-success { background: #28a745; }
        .btn-success:hover { background: #218838; }
        .result { margin-top: 20px; padding: 15px; background: #f8f9fa; border-radius: 8px; border: 1px solid #dee2e6; display: none; }
        .result pre { background: #e9ecef; padding: 10px; border-radius: 4px; overflow-x: auto; }
        .warning { background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; padding: 15px; border-radius: 8px; margin: 20px 0; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">🎵 抖音历史记录分析工具</h1>
            <p class="subtitle">修复版 - Python标准库实现</p>
        </div>
        
        <div class="card status">
            <h2>✅ 服务状态</h2>
            <p>服务器正常运行中！</p>
            <p>当前时间: <span id="time"></span></p>
            <p>服务器版本: v1.0.0-fixed</p>
        </div>
        
        <div class="warning">
            <strong>⚠️ 注意:</strong> 这是简化版本，使用Python标准库实现。要获得完整功能（抖音API、数据库、AI分析等），请解决pip依赖问题。
        </div>
        
        <div class="card">
            <h2>📡 API接口</h2>
            <div class="api-grid">
                <div class="api-item">
                    <div class="api-method">GET</div>
                    <div class="api-url">/health</div>
                    <p>健康检查接口</p>
                    <button class="btn" onclick="testAPI('/health')">测试</button>
                </div>
                <div class="api-item">
                    <div class="api-method">GET</div>
                    <div class="api-url">/api/v1/test</div>
                    <p>API功能测试</p>
                    <button class="btn" onclick="testAPI('/api/v1/test')">测试</button>
                </div>
                <div class="api-item">
                    <div class="api-method">GET</div>
                    <div class="api-url">/api/v1/status</div>
                    <p>系统状态信息</p>
                    <button class="btn" onclick="testAPI('/api/v1/status')">测试</button>
                </div>
                <div class="api-item">
                    <div class="api-method">POST</div>
                    <div class="api-url">/api/v1/echo</div>
                    <p>数据回显测试</p>
                    <button class="btn btn-success" onclick="testPOST()">测试</button>
                </div>
            </div>
        </div>
        
        <div class="card">
            <h2>🧪 功能测试</h2>
            <button class="btn" onclick="runAllTests()">运行所有测试</button>
            <button class="btn" onclick="clearResults()">清除结果</button>
            <div id="result" class="result"></div>
        </div>
    </div>
    
    <script>
        function updateTime() {
            document.getElementById('time').textContent = new Date().toLocaleString('zh-CN');
        }
        updateTime();
        setInterval(updateTime, 1000);
        
        async function testAPI(url) {
            try {
                showResult('正在测试: ' + url, null);
                const response = await fetch(url);
                const data = await response.json();
                showResult('✅ 测试成功: ' + url, data);
            } catch (error) {
                showResult('❌ 测试失败: ' + url, {error: error.message});
            }
        }
        
        async function testPOST() {
            try {
                const testData = {
                    message: 'Hello from frontend!',
                    timestamp: new Date().toISOString(),
                    test: true
                };
                showResult('正在测试 POST /api/v1/echo', null);
                const response = await fetch('/api/v1/echo', {
                    method: 'POST',
                    headers: {'Content-Type': 'application/json'},
                    body: JSON.stringify(testData)
                });
                const data = await response.json();
                showResult('✅ POST测试成功', data);
            } catch (error) {
                showResult('❌ POST测试失败', {error: error.message});
            }
        }
        
        async function runAllTests() {
            showResult('🚀 开始运行所有测试...', null);
            await testAPI('/health');
            await new Promise(resolve => setTimeout(resolve, 500));
            await testAPI('/api/v1/test');
            await new Promise(resolve => setTimeout(resolve, 500));
            await testAPI('/api/v1/status');
            await new Promise(resolve => setTimeout(resolve, 500));
            await testPOST();
            showResult('🎉 所有测试完成！', null);
        }
        
        function showResult(title, data) {
            const result = document.getElementById('result');
            let content = '<h3>' + title + '</h3>';
            if (data) {
                content += '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
            }
            result.innerHTML = content;
            result.style.display = 'block';
        }
        
        function clearResults() {
            document.getElementById('result').style.display = 'none';
        }
    </script>
</body>
</html>'''
        
        self.send_response(200)
        self.send_header('Content-Type', 'text/html; charset=utf-8')
        self.send_header('Content-Length', str(len(html.encode('utf-8'))))
        self.send_cors_headers()
        self.end_headers()
        self.wfile.write(html.encode('utf-8'))
    
    def get_system_status(self):
        """获取系统状态"""
        import platform
        
        status = {
            "success": True,
            "data": {
                "system": {
                    "platform": platform.system(),
                    "python_version": platform.python_version(),
                    "architecture": platform.architecture()[0]
                },
                "server": {
                    "type": "修复版Python服务器",
                    "port": 8000,
                    "version": "1.0.0-fixed"
                },
                "directories": {
                    "data": os.path.exists("data"),
                    "downloads": os.path.exists("downloads"), 
                    "logs": os.path.exists("logs")
                },
                "timestamp": datetime.now().isoformat()
            }
        }
        
        self.send_json_response(status)
    
    def send_json_response(self, data):
        """发送JSON响应"""
        json_data = json.dumps(data, ensure_ascii=False, indent=2)
        json_bytes = json_data.encode('utf-8')
        
        self.send_response(200)
        self.send_header('Content-Type', 'application/json; charset=utf-8')
        self.send_header('Content-Length', str(len(json_bytes)))
        self.send_cors_headers()
        self.end_headers()
        self.wfile.write(json_bytes)
    
    def send_error_response(self, code, message):
        """发送错误响应"""
        error_data = {
            "success": False,
            "error": message,
            "code": code,
            "timestamp": datetime.now().isoformat()
        }
        
        json_data = json.dumps(error_data, ensure_ascii=False)
        json_bytes = json_data.encode('utf-8')
        
        self.send_response(code)
        self.send_header('Content-Type', 'application/json; charset=utf-8')
        self.send_header('Content-Length', str(len(json_bytes)))
        self.send_cors_headers()
        self.end_headers()
        self.wfile.write(json_bytes)

def create_directories():
    """创建必要目录"""
    dirs = ["data", "downloads", "logs"]
    for dir_name in dirs:
        Path(dir_name).mkdir(exist_ok=True)
        print(f"✓ 创建目录: {dir_name}")

def main():
    """主函数"""
    print("=" * 60)
    print("🎵 抖音历史记录分析工具 - 修复版服务器")
    print("=" * 60)
    
    # 创建必要目录
    create_directories()
    
    # 启动服务器
    server_address = ('localhost', 8000)
    httpd = HTTPServer(server_address, FixedDouyinAPI)
    
    print(f"\n✅ 修复版服务器启动成功!")
    print(f"📍 访问地址: http://localhost:8000")
    print(f"🔧 已修复响应格式问题")
    print(f"📡 支持的接口:")
    print(f"   - GET  /health")
    print(f"   - GET  /api/v1/test")
    print(f"   - GET  /api/v1/status")
    print(f"   - POST /api/v1/echo")
    print(f"\n按 Ctrl+C 停止服务器")
    print("=" * 60)
    
    try:
        httpd.serve_forever()
    except KeyboardInterrupt:
        print("\n\n🛑 服务器已停止")
        httpd.shutdown()

if __name__ == "__main__":
    main()
