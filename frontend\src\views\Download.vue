<template>
  <div class="download-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h1>下载管理</h1>
        <p>管理您的视频下载任务</p>
      </div>
      <div class="header-actions">
        <el-button @click="handleClearCompleted" type="info" plain>
          <el-icon><Delete /></el-icon>
          清理已完成
        </el-button>
        <el-button @click="handlePauseAll" v-if="hasRunningTasks" type="warning">
          <el-icon><VideoPause /></el-icon>
          暂停全部
        </el-button>
        <el-button @click="handleResumeAll" v-else type="success">
          <el-icon><VideoPlay /></el-icon>
          继续全部
        </el-button>
      </div>
    </div>

    <!-- 快速下载 -->
    <div class="quick-download-section">
      <el-card class="quick-card">
        <template #header>
          <span>快速下载</span>
        </template>
        <div class="quick-content">
          <div class="url-input">
            <el-input
              v-model="downloadUrl"
              placeholder="粘贴抖音视频链接或输入视频ID"
              clearable
              @keyup.enter="handleQuickDownload"
            >
              <template #prefix>
                <el-icon><Link /></el-icon>
              </template>
              <template #append>
                <el-button @click="handleQuickDownload" :loading="parsing" type="primary">
                  下载
                </el-button>
              </template>
            </el-input>
          </div>

          <div class="download-options">
            <el-checkbox-group v-model="downloadOptions">
              <el-checkbox label="video">视频</el-checkbox>
              <el-checkbox label="audio">音频</el-checkbox>
              <el-checkbox label="cover">封面</el-checkbox>
              <el-checkbox label="subtitle">字幕</el-checkbox>
            </el-checkbox-group>

            <el-select v-model="videoQuality" placeholder="视频质量" style="width: 120px">
              <el-option label="原画" value="origin" />
              <el-option label="超清" value="1080p" />
              <el-option label="高清" value="720p" />
              <el-option label="标清" value="480p" />
            </el-select>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 下载统计 -->
    <div class="stats-section">
      <el-row :gutter="16">
        <el-col :xs="12" :sm="6" :md="6" :lg="6">
          <div class="stat-card">
            <div class="stat-icon total">
              <el-icon><Download /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ stats.total }}</div>
              <div class="stat-label">总任务</div>
            </div>
          </div>
        </el-col>

        <el-col :xs="12" :sm="6" :md="6" :lg="6">
          <div class="stat-card">
            <div class="stat-icon running">
              <el-icon><Loading /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ stats.running }}</div>
              <div class="stat-label">下载中</div>
            </div>
          </div>
        </el-col>

        <el-col :xs="12" :sm="6" :md="6" :lg="6">
          <div class="stat-card">
            <div class="stat-icon completed">
              <el-icon><Check /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ stats.completed }}</div>
              <div class="stat-label">已完成</div>
            </div>
          </div>
        </el-col>

        <el-col :xs="12" :sm="6" :md="6" :lg="6">
          <div class="stat-card">
            <div class="stat-icon failed">
              <el-icon><Close /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ stats.failed }}</div>
              <div class="stat-label">失败</div>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 任务筛选 -->
    <div class="filter-section">
      <div class="filter-bar">
        <el-radio-group v-model="statusFilter" @change="handleFilterChange">
          <el-radio-button label="all">全部</el-radio-button>
          <el-radio-button label="running">下载中</el-radio-button>
          <el-radio-button label="completed">已完成</el-radio-button>
          <el-radio-button label="failed">失败</el-radio-button>
          <el-radio-button label="paused">已暂停</el-radio-button>
        </el-radio-group>

        <div class="filter-actions">
          <el-input
            v-model="searchKeyword"
            placeholder="搜索任务..."
            clearable
            style="width: 200px"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>

          <el-button @click="handleRefresh" :loading="loading">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
        </div>
      </div>
    </div>

    <!-- 下载任务列表 -->
    <div class="tasks-section">
      <div v-if="filteredTasks.length === 0" class="empty-state">
        <el-empty description="暂无下载任务">
          <el-button type="primary" @click="$router.push('/history')">
            <el-icon><VideoPlay /></el-icon>
            去历史记录选择视频
          </el-button>
        </el-empty>
      </div>

      <div v-else class="tasks-list">
        <div v-for="task in paginatedTasks" :key="task.id" class="task-item">
          <div class="task-cover">
            <img :src="task.cover_url || '/default-cover.jpg'" :alt="task.title" />
            <div class="task-status" :class="task.status">
              <el-icon v-if="task.status === 'running'"><Loading /></el-icon>
              <el-icon v-else-if="task.status === 'completed'"><Check /></el-icon>
              <el-icon v-else-if="task.status === 'failed'"><Close /></el-icon>
              <el-icon v-else-if="task.status === 'paused'"><VideoPause /></el-icon>
              <el-icon v-else><Clock /></el-icon>
            </div>
          </div>

          <div class="task-info">
            <div class="task-title">{{ task.title || '无标题' }}</div>
            <div class="task-meta">
              <span class="task-author">{{ task.author }}</span>
              <span class="task-size">{{ formatFileSize(task.file_size) }}</span>
              <span class="task-quality">{{ task.quality }}</span>
            </div>

            <div class="task-progress">
              <el-progress
                :percentage="task.progress"
                :status="getProgressStatus(task.status)"
                :show-text="false"
              />
              <div class="progress-info">
                <span class="progress-text">{{ task.progress }}%</span>
                <span class="progress-speed" v-if="task.status === 'running'">
                  {{ formatSpeed(task.download_speed) }}
                </span>
              </div>
            </div>

            <div class="task-time">
              <span>创建时间：{{ formatTime(task.created_at) }}</span>
              <span v-if="task.completed_at">完成时间：{{ formatTime(task.completed_at) }}</span>
            </div>
          </div>

          <div class="task-actions">
            <el-button
              v-if="task.status === 'running'"
              @click="handlePauseTask(task)"
              type="warning"
              size="small"
            >
              <el-icon><VideoPause /></el-icon>
              暂停
            </el-button>

            <el-button
              v-else-if="task.status === 'paused' || task.status === 'failed'"
              @click="handleResumeTask(task)"
              type="success"
              size="small"
            >
              <el-icon><VideoPlay /></el-icon>
              继续
            </el-button>

            <el-button
              v-if="task.status === 'completed'"
              @click="handleOpenFile(task)"
              type="primary"
              size="small"
            >
              <el-icon><FolderOpened /></el-icon>
              打开
            </el-button>

            <el-button
              @click="handleDeleteTask(task)"
              type="danger"
              size="small"
            >
              <el-icon><Delete /></el-icon>
              删除
            </el-button>
          </div>
        </div>
      </div>

      <!-- 分页 -->
      <div v-if="filteredTasks.length > 0" class="pagination-section">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="filteredTasks.length"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
import { useRoute } from 'vue-router'
import { useDouyinStore } from '@/stores/douyin'
import { downloadAPI } from '@/utils/api'
import { ElMessage, ElMessageBox } from 'element-plus'
import dayjs from 'dayjs'

const route = useRoute()
const douyinStore = useDouyinStore()

// 响应式数据
const loading = ref(false)
const parsing = ref(false)
const downloadUrl = ref('')
const downloadOptions = ref(['video'])
const videoQuality = ref('720p')
const statusFilter = ref('all')
const searchKeyword = ref('')
const currentPage = ref(1)
const pageSize = ref(20)
const tasks = ref([])
const updateTimer = ref(null)

// 统计数据
const stats = reactive({
  total: 0,
  running: 0,
  completed: 0,
  failed: 0
})

// 计算属性
const hasRunningTasks = computed(() => stats.running > 0)

const filteredTasks = computed(() => {
  let result = [...tasks.value]

  // 状态筛选
  if (statusFilter.value !== 'all') {
    result = result.filter(task => task.status === statusFilter.value)
  }

  // 搜索筛选
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    result = result.filter(task =>
      (task.title && task.title.toLowerCase().includes(keyword)) ||
      (task.author && task.author.toLowerCase().includes(keyword))
    )
  }

  return result
})

const paginatedTasks = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return filteredTasks.value.slice(start, end)
})

// 方法
const formatFileSize = (bytes) => {
  if (!bytes) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const formatSpeed = (bytesPerSecond) => {
  if (!bytesPerSecond) return '0 KB/s'
  return formatFileSize(bytesPerSecond) + '/s'
}

const formatTime = (time) => {
  return dayjs(time).format('YYYY-MM-DD HH:mm:ss')
}

const getProgressStatus = (status) => {
  switch (status) {
    case 'completed':
      return 'success'
    case 'failed':
      return 'exception'
    case 'running':
      return ''
    default:
      return ''
  }
}

// 事件处理
const handleQuickDownload = async () => {
  if (!downloadUrl.value.trim()) {
    ElMessage.warning('请输入视频链接')
    return
  }

  try {
    parsing.value = true

    const response = await downloadAPI.createTask({
      url: downloadUrl.value,
      options: downloadOptions.value,
      quality: videoQuality.value
    })

    if (response.success) {
      ElMessage.success('下载任务创建成功')
      downloadUrl.value = ''
      await loadTasks()
    }
  } catch (error) {
    ElMessage.error(error.message || '创建下载任务失败')
  } finally {
    parsing.value = false
  }
}

const handlePauseTask = async (task) => {
  try {
    const response = await downloadAPI.pauseTask(task.id)
    if (response.success) {
      ElMessage.success('任务已暂停')
      await loadTasks()
    }
  } catch (error) {
    ElMessage.error('暂停任务失败')
  }
}

const handleResumeTask = async (task) => {
  try {
    const response = await downloadAPI.resumeTask(task.id)
    if (response.success) {
      ElMessage.success('任务已继续')
      await loadTasks()
    }
  } catch (error) {
    ElMessage.error('继续任务失败')
  }
}

const handleDeleteTask = async (task) => {
  try {
    await ElMessageBox.confirm(
      '确定要删除这个下载任务吗？',
      '删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const response = await downloadAPI.deleteTask(task.id)
    if (response.success) {
      ElMessage.success('任务已删除')
      await loadTasks()
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除任务失败')
    }
  }
}

const handleOpenFile = async (task) => {
  try {
    const response = await downloadAPI.openFile(task.id)
    if (response.success) {
      ElMessage.success('文件已打开')
    }
  } catch (error) {
    ElMessage.error('打开文件失败')
  }
}

const handlePauseAll = async () => {
  try {
    const response = await downloadAPI.pauseAll()
    if (response.success) {
      ElMessage.success('所有任务已暂停')
      await loadTasks()
    }
  } catch (error) {
    ElMessage.error('暂停失败')
  }
}

const handleResumeAll = async () => {
  try {
    const response = await downloadAPI.resumeAll()
    if (response.success) {
      ElMessage.success('所有任务已继续')
      await loadTasks()
    }
  } catch (error) {
    ElMessage.error('继续失败')
  }
}

const handleClearCompleted = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要清理所有已完成的任务吗？',
      '清理确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const response = await downloadAPI.clearCompleted()
    if (response.success) {
      ElMessage.success('已完成任务已清理')
      await loadTasks()
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('清理失败')
    }
  }
}

const handleFilterChange = () => {
  currentPage.value = 1
}

const handleRefresh = () => {
  loadTasks()
}

const handleSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1
}

const handleCurrentChange = (page) => {
  currentPage.value = page
}

// 加载任务列表
const loadTasks = async () => {
  try {
    loading.value = true
    const response = await downloadAPI.getTasks()

    if (response.success) {
      tasks.value = response.data.tasks || []

      // 更新统计数据
      stats.total = tasks.value.length
      stats.running = tasks.value.filter(t => t.status === 'running').length
      stats.completed = tasks.value.filter(t => t.status === 'completed').length
      stats.failed = tasks.value.filter(t => t.status === 'failed').length
    }
  } catch (error) {
    ElMessage.error('加载任务列表失败')
  } finally {
    loading.value = false
  }
}

// 启动定时更新
const startUpdateTimer = () => {
  updateTimer.value = setInterval(() => {
    if (stats.running > 0) {
      loadTasks()
    }
  }, 2000) // 每2秒更新一次
}

// 停止定时更新
const stopUpdateTimer = () => {
  if (updateTimer.value) {
    clearInterval(updateTimer.value)
    updateTimer.value = null
  }
}

// 组件挂载时
onMounted(async () => {
  await loadTasks()
  startUpdateTimer()

  // 检查路由参数，如果有视频ID则自动添加下载任务
  if (route.query.video_id) {
    // 处理单个视频下载
    downloadUrl.value = route.query.video_id
  } else if (route.query.video_ids) {
    // 处理批量视频下载
    const videoIds = route.query.video_ids.split(',')
    for (const videoId of videoIds) {
      try {
        await downloadAPI.createTask({
          url: videoId,
          options: downloadOptions.value,
          quality: videoQuality.value
        })
      } catch (error) {
        console.error('创建下载任务失败:', error)
      }
    }
    await loadTasks()
    ElMessage.success(`已创建 ${videoIds.length} 个下载任务`)
  }
})

// 组件卸载时
onUnmounted(() => {
  stopUpdateTimer()
})
</script>

<style lang="scss" scoped>
.download-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 16px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  flex-wrap: wrap;
  gap: 16px;
  
  .header-content {
    h1 {
      font-size: 28px;
      font-weight: 600;
      color: var(--text-primary);
      margin: 0 0 8px 0;
    }
    
    p {
      font-size: 16px;
      color: var(--text-secondary);
      margin: 0;
    }
  }
  
  .header-actions {
    display: flex;
    gap: 12px;
    
    .el-button {
      &.el-button--primary, &.el-button--success {
        background-color: var(--primary-color);
        border-color: var(--primary-color);
        
        &:hover, &:focus {
          background-color: var(--primary-light);
          border-color: var(--primary-light);
        }
      }
    }
  }
}

.quick-download-section {
  margin-bottom: 24px;
  
  .quick-card {
    border: none;
    border-radius: var(--border-radius-large);
    box-shadow: var(--shadow-light);
    overflow: hidden;
    
    :deep(.el-card__header) {
      padding: 16px;
      border-bottom: 1px solid var(--border-lighter);
      font-size: 16px;
      font-weight: 500;
      color: var(--text-primary);
    }
    
    :deep(.el-card__body) {
      padding: 20px;
    }
    
    .quick-content {
      .url-input {
        margin-bottom: 16px;
        
        :deep(.el-input__wrapper) {
          border-radius: var(--border-radius-base);
        }
        
        :deep(.el-input-group__append) {
          button {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
            color: white;
            
            &:hover {
              background-color: var(--primary-light);
              border-color: var(--primary-light);
            }
          }
        }
      }
      
      .download-options {
        display: flex;
        justify-content: space-between;
        flex-wrap: wrap;
        gap: 12px;
        
        :deep(.el-checkbox) {
          margin-right: 16px;
          
          &:last-child {
            margin-right: 0;
          }
          
          .el-checkbox__input.is-checked .el-checkbox__inner {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
          }
          
          .el-checkbox__input.is-checked + .el-checkbox__label {
            color: var(--primary-color);
          }
        }
        
        :deep(.el-select) {
          .el-input__wrapper.is-focus {
            box-shadow: 0 0 0 1px var(--primary-color) inset;
          }
        }
      }
    }
  }
}

.stats-section {
  margin-bottom: 24px;
  
  .stat-card {
    background-color: var(--bg-white);
    border-radius: var(--border-radius-large);
    padding: 16px;
    box-shadow: var(--shadow-light);
    display: flex;
    align-items: center;
    gap: 16px;
    height: 100%;
    transition: transform 0.2s;
    
    &:hover {
      transform: translateY(-5px);
    }
    
    .stat-icon {
      width: 48px;
      height: 48px;
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 24px;
      color: white;
      
      &.total {
        background: linear-gradient(135deg, var(--primary-color), #ff5050);
      }
      
      &.running {
        background: linear-gradient(135deg, #4facfe, #00f2fe);
      }
      
      &.completed {
        background: linear-gradient(135deg, #84fab0, #8fd3f4);
      }
      
      &.failed {
        background: linear-gradient(135deg, #ff9a9e, #fad0c4);
      }
    }
    
    .stat-info {
      flex: 1;
      
      .stat-value {
        font-size: 24px;
        font-weight: 600;
        color: var(--text-primary);
        line-height: 1;
      }
      
      .stat-label {
        font-size: 12px;
        color: var(--text-secondary);
        margin-top: 4px;
      }
    }
  }
}

.filter-section {
  margin-bottom: 24px;
  
  .filter-bar {
    background-color: var(--bg-white);
    border-radius: var(--border-radius-large);
    padding: 16px;
    box-shadow: var(--shadow-light);
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 16px;
    
    :deep(.el-radio-button__inner) {
      border-radius: 20px;
      padding: 8px 16px;
    }
    
    :deep(.el-radio-button:first-child .el-radio-button__inner) {
      border-radius: 20px 0 0 20px;
    }
    
    :deep(.el-radio-button:last-child .el-radio-button__inner) {
      border-radius: 0 20px 20px 0;
    }
    
    :deep(.el-radio-button__original) {
      opacity: 0;
    }
    
    :deep(.el-radio-button__inner:hover) {
      color: var(--primary-color);
    }
    
    :deep(.el-radio-button__orig-radio:checked + .el-radio-button__inner) {
      background-color: var(--primary-color);
      border-color: var(--primary-color);
      box-shadow: -1px 0 0 0 var(--primary-color);
    }
    
    .filter-actions {
      display: flex;
      gap: 12px;
      
      :deep(.el-input__wrapper) {
        border-radius: 20px;
      }
    }
  }
}

.tasks-section {
  .empty-state {
    background-color: var(--bg-white);
    border-radius: var(--border-radius-large);
    padding: 40px;
    box-shadow: var(--shadow-light);
    text-align: center;
    
    .el-button {
      margin-top: 16px;
      background-color: var(--primary-color);
      border-color: var(--primary-color);
      
      &:hover, &:focus {
        background-color: var(--primary-light);
        border-color: var(--primary-light);
      }
    }
  }
  
  .tasks-list {
    .task-item {
      background-color: var(--bg-white);
      border-radius: var(--border-radius-large);
      padding: 16px;
      box-shadow: var(--shadow-light);
      display: flex;
      gap: 16px;
      margin-bottom: 16px;
      transition: transform 0.2s;
      
      &:hover {
        transform: translateY(-5px);
      }
      
      .task-cover {
        position: relative;
        width: 120px;
        height: 120px;
        border-radius: var(--border-radius-base);
        overflow: hidden;
        flex-shrink: 0;
        
        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
        
        .task-status {
          position: absolute;
          top: 8px;
          right: 8px;
          width: 24px;
          height: 24px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
          font-size: 12px;
          
          &.running {
            background-color: #409eff;
          }
          
          &.completed {
            background-color: #67c23a;
          }
          
          &.failed {
            background-color: #f56c6c;
          }
          
          &.paused {
            background-color: #e6a23c;
          }
          
          &.pending {
            background-color: #909399;
          }
        }
      }
      
      .task-info {
        flex: 1;
        display: flex;
        flex-direction: column;
        
        .task-title {
          font-size: 16px;
          font-weight: 500;
          color: var(--text-primary);
          margin-bottom: 8px;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
        }
        
        .task-meta {
          display: flex;
          flex-wrap: wrap;
          gap: 12px;
          margin-bottom: 12px;
          
          span {
            font-size: 12px;
            color: var(--text-secondary);
            display: flex;
            align-items: center;
            gap: 4px;
          }
        }
        
        .task-progress {
          margin-bottom: 12px;
          
          :deep(.el-progress-bar__outer) {
            border-radius: 4px;
            background-color: var(--border-lighter);
          }
          
          :deep(.el-progress-bar__inner) {
            border-radius: 4px;
          }
          
          :deep(.el-progress--success .el-progress-bar__inner) {
            background-color: #67c23a;
          }
          
          :deep(.el-progress--exception .el-progress-bar__inner) {
            background-color: #f56c6c;
          }
          
          :deep(.el-progress--warning .el-progress-bar__inner) {
            background-color: #e6a23c;
          }
          
          .progress-info {
            display: flex;
            justify-content: space-between;
            margin-top: 4px;
            
            span {
              font-size: 12px;
              color: var(--text-secondary);
            }
          }
        }
        
        .task-time {
          margin-top: auto;
          display: flex;
          flex-wrap: wrap;
          gap: 16px;
          
          span {
            font-size: 12px;
            color: var(--text-secondary);
          }
        }
      }
      
      .task-actions {
        display: flex;
        flex-direction: column;
        gap: 8px;
        justify-content: center;
        
        .el-button {
          padding: 8px;
          
          &.el-button--primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
            
            &:hover {
              background-color: var(--primary-light);
              border-color: var(--primary-light);
            }
          }
        }
      }
    }
  }
}

.pagination-section {
  display: flex;
  justify-content: center;
  margin-top: 24px;
  
  :deep(.el-pagination.is-background .el-pager li:not(.is-disabled).is-active) {
    background-color: var(--primary-color);
  }
}

// 响应式
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    
    .header-actions {
      width: 100%;
      justify-content: space-between;
    }
  }
  
  .download-options {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .filter-bar {
    flex-direction: column;
    align-items: flex-start;
    
    .el-radio-group {
      width: 100%;
      overflow-x: auto;
      white-space: nowrap;
    }
    
    .filter-actions {
      width: 100%;
      flex-wrap: wrap;
    }
  }
  
  .task-item {
    flex-direction: column;
    
    .task-cover {
      width: 100% !important;
      height: 180px !important;
    }
    
    .task-actions {
      flex-direction: row !important;
      justify-content: flex-end !important;
    }
  }
}

// 暗色主题适配
.dark {
  .task-item {
    .task-cover {
      background-color: #2a2a2a;
    }
  }
}
</style>
